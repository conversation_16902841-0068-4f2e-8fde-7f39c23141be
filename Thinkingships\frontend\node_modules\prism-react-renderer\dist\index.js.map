{"version": 3, "sources": ["../../../node_modules/.pnpm/prismjs@1.29.0_patch_hash=vrxx3pzkik6jpmgpayxfjunetu/node_modules/prismjs/prism.js", "../src/index.ts", "../src/prism-langs.ts", "../src/themes/index.ts", "../src/themes/dracula.ts", "../src/themes/duotoneDark.ts", "../src/themes/duotoneLight.ts", "../src/themes/github.ts", "../src/themes/nightOwl.ts", "../src/themes/nightOwlLight.ts", "../src/themes/oceanicNext.ts", "../src/themes/okaidia.ts", "../src/themes/palenight.ts", "../src/themes/shadesOfPurple.ts", "../src/themes/synthwave84.ts", "../src/themes/ultramin.ts", "../src/themes/vsDark.ts", "../src/themes/vsLight.ts", "../src/themes/jettwaveDark.ts", "../src/themes/jettwaveLight.ts", "../src/themes/oneDark.ts", "../src/themes/oneLight.ts", "../src/themes/gruvboxMaterialDark.ts", "../src/themes/gruvboxMaterialLight.ts", "../src/components/useGetLineProps.ts", "../src/components/useGetTokenProps.ts", "../src/utils/normalizeTokens.ts", "../src/components/useTokenize.ts", "../src/utils/themeToDict.ts", "../src/components/highlight.ts"], "sourcesContent": ["\n/* **********************************************\n     Begin prism-core.js\n********************************************** */\n\n/// <reference lib=\"WebWorker\"/>\n\n/**\n * Prism: Lightweight, robust, elegant syntax highlighting\n *\n * @license MIT <https://opensource.org/licenses/MIT>\n * <AUTHOR> Verou <https://lea.verou.me>\n * @namespace\n * @public\n */\nvar Prism = (function () {\n\n\t// Private helper vars\n\tvar lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n\tvar uniqueId = 0;\n\n\t// The grammar object for plaintext\n\tvar plainTextGrammar = {};\n\n\n\tvar _ = {\n\t\t/**\n\t\t * A namespace for utility methods.\n\t\t *\n\t\t * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n\t\t * change or disappear at any time.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t */\n\t\tutil: {\n\t\t\tencode: function encode(tokens) {\n\t\t\t\tif (tokens instanceof Token) {\n\t\t\t\t\treturn new Token(tokens.type, encode(tokens.content), tokens.alias);\n\t\t\t\t} else if (Array.isArray(tokens)) {\n\t\t\t\t\treturn tokens.map(encode);\n\t\t\t\t} else {\n\t\t\t\t\treturn tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the name of the type of the given value.\n\t\t\t *\n\t\t\t * @param {any} o\n\t\t\t * @returns {string}\n\t\t\t * @example\n\t\t\t * type(null)      === 'Null'\n\t\t\t * type(undefined) === 'Undefined'\n\t\t\t * type(123)       === 'Number'\n\t\t\t * type('foo')     === 'String'\n\t\t\t * type(true)      === 'Boolean'\n\t\t\t * type([1, 2])    === 'Array'\n\t\t\t * type({})        === 'Object'\n\t\t\t * type(String)    === 'Function'\n\t\t\t * type(/abc+/)    === 'RegExp'\n\t\t\t */\n\t\t\ttype: function (o) {\n\t\t\t\treturn Object.prototype.toString.call(o).slice(8, -1);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns a unique number for the given object. Later calls will still return the same number.\n\t\t\t *\n\t\t\t * @param {Object} obj\n\t\t\t * @returns {number}\n\t\t\t */\n\t\t\tobjId: function (obj) {\n\t\t\t\tif (!obj['__id']) {\n\t\t\t\t\tObject.defineProperty(obj, '__id', { value: ++uniqueId });\n\t\t\t\t}\n\t\t\t\treturn obj['__id'];\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Creates a deep clone of the given object.\n\t\t\t *\n\t\t\t * The main intended use of this function is to clone language definitions.\n\t\t\t *\n\t\t\t * @param {T} o\n\t\t\t * @param {Record<number, any>} [visited]\n\t\t\t * @returns {T}\n\t\t\t * @template T\n\t\t\t */\n\t\t\tclone: function deepClone(o, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar clone; var id;\n\t\t\t\tswitch (_.util.type(o)) {\n\t\t\t\t\tcase 'Object':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = /** @type {Record<string, any>} */ ({});\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\tfor (var key in o) {\n\t\t\t\t\t\t\tif (o.hasOwnProperty(key)) {\n\t\t\t\t\t\t\t\tclone[key] = deepClone(o[key], visited);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tcase 'Array':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = [];\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\t(/** @type {Array} */(/** @type {any} */(o))).forEach(function (v, i) {\n\t\t\t\t\t\t\tclone[i] = deepClone(v, visited);\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn o;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n\t\t\t *\n\t\t\t * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @returns {string}\n\t\t\t */\n\t\t\tgetLanguage: function (element) {\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar m = lang.exec(element.className);\n\t\t\t\t\tif (m) {\n\t\t\t\t\t\treturn m[1].toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn 'none';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Sets the Prism `language-xxxx` class of the given element.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} language\n\t\t\t * @returns {void}\n\t\t\t */\n\t\t\tsetLanguage: function (element, language) {\n\t\t\t\t// remove all `language-xxxx` classes\n\t\t\t\t// (this might leave behind a leading space)\n\t\t\t\telement.className = element.className.replace(RegExp(lang, 'gi'), '');\n\n\t\t\t\t// add the new `language-xxxx` class\n\t\t\t\t// (using `classList` will automatically clean up spaces for us)\n\t\t\t\telement.classList.add('language-' + language);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns whether a given class is active for `element`.\n\t\t\t *\n\t\t\t * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n\t\t\t * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n\t\t\t * given class is just the given class with a `no-` prefix.\n\t\t\t *\n\t\t\t * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n\t\t\t * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n\t\t\t * ancestors have the given class or the negated version of it, then the default activation will be returned.\n\t\t\t *\n\t\t\t * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n\t\t\t * version of it, the class is considered active.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} className\n\t\t\t * @param {boolean} [defaultActivation=false]\n\t\t\t * @returns {boolean}\n\t\t\t */\n\t\t\tisActive: function (element, className, defaultActivation) {\n\t\t\t\tvar no = 'no-' + className;\n\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar classList = element.classList;\n\t\t\t\t\tif (classList.contains(className)) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tif (classList.contains(no)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn !!defaultActivation;\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tlanguages: {\n\t\t\t/**\n\t\t\t * The grammar for plain, unformatted text.\n\t\t\t */\n\t\t\tplain: plainTextGrammar,\n\t\t\tplaintext: plainTextGrammar,\n\t\t\ttext: plainTextGrammar,\n\t\t\ttxt: plainTextGrammar,\n\n\t\t\t/**\n\t\t\t * Creates a deep copy of the language with the given id and appends the given tokens.\n\t\t\t *\n\t\t\t * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n\t\t\t * will be overwritten at its original position.\n\t\t\t *\n\t\t\t * ## Best practices\n\t\t\t *\n\t\t\t * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n\t\t\t * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n\t\t\t * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n\t\t\t *\n\t\t\t * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n\t\t\t * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n\t\t\t *\n\t\t\t * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n\t\t\t * @param {Grammar} redef The new tokens to append.\n\t\t\t * @returns {Grammar} The new language created.\n\t\t\t * @public\n\t\t\t * @example\n\t\t\t * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n\t\t\t *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n\t\t\t *     // at its original position\n\t\t\t *     'comment': { ... },\n\t\t\t *     // CSS doesn't have a 'color' token, so this token will be appended\n\t\t\t *     'color': /\\b(?:red|green|blue)\\b/\n\t\t\t * });\n\t\t\t */\n\t\t\textend: function (id, redef) {\n\t\t\t\tvar lang = _.util.clone(_.languages[id]);\n\n\t\t\t\tfor (var key in redef) {\n\t\t\t\t\tlang[key] = redef[key];\n\t\t\t\t}\n\n\t\t\t\treturn lang;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Inserts tokens _before_ another token in a language definition or any other grammar.\n\t\t\t *\n\t\t\t * ## Usage\n\t\t\t *\n\t\t\t * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n\t\t\t * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n\t\t\t * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n\t\t\t * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n\t\t\t * this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.markup.style = {\n\t\t\t *     // token\n\t\t\t * };\n\t\t\t * ```\n\t\t\t *\n\t\t\t * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n\t\t\t * before existing tokens. For the CSS example above, you would use it like this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'cdata', {\n\t\t\t *     'style': {\n\t\t\t *         // token\n\t\t\t *     }\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Special cases\n\t\t\t *\n\t\t\t * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n\t\t\t * will be ignored.\n\t\t\t *\n\t\t\t * This behavior can be used to insert tokens after `before`:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'comment', {\n\t\t\t *     'comment': Prism.languages.markup.comment,\n\t\t\t *     // tokens after 'comment'\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Limitations\n\t\t\t *\n\t\t\t * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n\t\t\t * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n\t\t\t * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n\t\t\t * deleting properties which is necessary to insert at arbitrary positions.\n\t\t\t *\n\t\t\t * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n\t\t\t * Instead, it will create a new object and replace all references to the target object with the new one. This\n\t\t\t * can be done without temporarily deleting properties, so the iteration order is well-defined.\n\t\t\t *\n\t\t\t * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n\t\t\t * you hold the target object in a variable, then the value of the variable will not change.\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * var oldMarkup = Prism.languages.markup;\n\t\t\t * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n\t\t\t *\n\t\t\t * assert(oldMarkup !== Prism.languages.markup);\n\t\t\t * assert(newMarkup === Prism.languages.markup);\n\t\t\t * ```\n\t\t\t *\n\t\t\t * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n\t\t\t * object to be modified.\n\t\t\t * @param {string} before The key to insert before.\n\t\t\t * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n\t\t\t * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n\t\t\t * object to be modified.\n\t\t\t *\n\t\t\t * Defaults to `Prism.languages`.\n\t\t\t * @returns {Grammar} The new grammar object.\n\t\t\t * @public\n\t\t\t */\n\t\t\tinsertBefore: function (inside, before, insert, root) {\n\t\t\t\troot = root || /** @type {any} */ (_.languages);\n\t\t\t\tvar grammar = root[inside];\n\t\t\t\t/** @type {Grammar} */\n\t\t\t\tvar ret = {};\n\n\t\t\t\tfor (var token in grammar) {\n\t\t\t\t\tif (grammar.hasOwnProperty(token)) {\n\n\t\t\t\t\t\tif (token == before) {\n\t\t\t\t\t\t\tfor (var newToken in insert) {\n\t\t\t\t\t\t\t\tif (insert.hasOwnProperty(newToken)) {\n\t\t\t\t\t\t\t\t\tret[newToken] = insert[newToken];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Do not insert token which also occur in insert. See #1525\n\t\t\t\t\t\tif (!insert.hasOwnProperty(token)) {\n\t\t\t\t\t\t\tret[token] = grammar[token];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar old = root[inside];\n\t\t\t\troot[inside] = ret;\n\n\t\t\t\t// Update references in other language definitions\n\t\t\t\t_.languages.DFS(_.languages, function (key, value) {\n\t\t\t\t\tif (value === old && key != inside) {\n\t\t\t\t\t\tthis[key] = ret;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn ret;\n\t\t\t},\n\n\t\t\t// Traverse a language definition with Depth First Search\n\t\t\tDFS: function DFS(o, callback, type, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar objId = _.util.objId;\n\n\t\t\t\tfor (var i in o) {\n\t\t\t\t\tif (o.hasOwnProperty(i)) {\n\t\t\t\t\t\tcallback.call(o, i, o[i], type || i);\n\n\t\t\t\t\t\tvar property = o[i];\n\t\t\t\t\t\tvar propertyType = _.util.type(property);\n\n\t\t\t\t\t\tif (propertyType === 'Object' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, null, visited);\n\t\t\t\t\t\t} else if (propertyType === 'Array' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, i, visited);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tplugins: {},\n\n\t\t/**\n\t\t * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns a string with the HTML produced.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-tokenize`\n\t\t * 2. `after-tokenize`\n\t\t * 3. `wrap`: On each {@link Token}.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @param {string} language The name of the language definition passed to `grammar`.\n\t\t * @returns {string} The highlighted HTML.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n\t\t */\n\t\thighlight: function (text, grammar, language) {\n\t\t\tvar env = {\n\t\t\t\tcode: text,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tlanguage: language\n\t\t\t};\n\t\t\t_.hooks.run('before-tokenize', env);\n\t\t\tif (!env.grammar) {\n\t\t\t\tthrow new Error('The language \"' + env.language + '\" has no grammar.');\n\t\t\t}\n\t\t\tenv.tokens = _.tokenize(env.code, env.grammar);\n\t\t\t_.hooks.run('after-tokenize', env);\n\t\t\treturn Token.stringify(_.util.encode(env.tokens), env.language);\n\t\t},\n\n\t\t/**\n\t\t * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns an array with the tokenized code.\n\t\t *\n\t\t * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n\t\t *\n\t\t * This method could be useful in other contexts as well, as a very crude parser.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @returns {TokenStream} An array of strings and tokens, a token stream.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * let code = `var foo = 0;`;\n\t\t * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n\t\t * tokens.forEach(token => {\n\t\t *     if (token instanceof Prism.Token && token.type === 'number') {\n\t\t *         console.log(`Found numeric literal: ${token.content}`);\n\t\t *     }\n\t\t * });\n\t\t */\n\t\ttokenize: function (text, grammar) {\n\t\t\tvar rest = grammar.rest;\n\t\t\tif (rest) {\n\t\t\t\tfor (var token in rest) {\n\t\t\t\t\tgrammar[token] = rest[token];\n\t\t\t\t}\n\n\t\t\t\tdelete grammar.rest;\n\t\t\t}\n\n\t\t\tvar tokenList = new LinkedList();\n\t\t\taddAfter(tokenList, tokenList.head, text);\n\n\t\t\tmatchGrammar(text, tokenList, grammar, tokenList.head, 0);\n\n\t\t\treturn toArray(tokenList);\n\t\t},\n\n\t\t/**\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thooks: {\n\t\t\tall: {},\n\n\t\t\t/**\n\t\t\t * Adds the given callback to the list of callbacks for the given hook.\n\t\t\t *\n\t\t\t * The callback will be invoked when the hook it is registered for is run.\n\t\t\t * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n\t\t\t *\n\t\t\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {HookCallback} callback The callback function which is given environment variables.\n\t\t\t * @public\n\t\t\t */\n\t\t\tadd: function (name, callback) {\n\t\t\t\tvar hooks = _.hooks.all;\n\n\t\t\t\thooks[name] = hooks[name] || [];\n\n\t\t\t\thooks[name].push(callback);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Runs a hook invoking all registered callbacks with the given environment variables.\n\t\t\t *\n\t\t\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n\t\t\t * @public\n\t\t\t */\n\t\t\trun: function (name, env) {\n\t\t\t\tvar callbacks = _.hooks.all[name];\n\n\t\t\t\tif (!callbacks || !callbacks.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tfor (var i = 0, callback; (callback = callbacks[i++]);) {\n\t\t\t\t\tcallback(env);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tToken: Token\n\t};\n\n\t// Typescript note:\n\t// The following can be used to import the Token type in JSDoc:\n\t//\n\t//   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n\t/**\n\t * Creates a new token.\n\t *\n\t * @param {string} type See {@link Token#type type}\n\t * @param {string | TokenStream} content See {@link Token#content content}\n\t * @param {string|string[]} [alias] The alias(es) of the token.\n\t * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n\t * @class\n\t * @global\n\t * @public\n\t */\n\tfunction Token(type, content, alias, matchedStr) {\n\t\t/**\n\t\t * The type of the token.\n\t\t *\n\t\t * This is usually the key of a pattern in a {@link Grammar}.\n\t\t *\n\t\t * @type {string}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.type = type;\n\t\t/**\n\t\t * The strings or tokens contained by this token.\n\t\t *\n\t\t * This will be a token stream if the pattern matched also defined an `inside` grammar.\n\t\t *\n\t\t * @type {string | TokenStream}\n\t\t * @public\n\t\t */\n\t\tthis.content = content;\n\t\t/**\n\t\t * The alias(es) of the token.\n\t\t *\n\t\t * @type {string|string[]}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.alias = alias;\n\t\t// Copy of the full string this token was created from\n\t\tthis.length = (matchedStr || '').length | 0;\n\t}\n\n\t/**\n\t * A token stream is an array of strings and {@link Token Token} objects.\n\t *\n\t * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n\t * them.\n\t *\n\t * 1. No adjacent strings.\n\t * 2. No empty strings.\n\t *\n\t *    The only exception here is the token stream that only contains the empty string and nothing else.\n\t *\n\t * @typedef {Array<string | Token>} TokenStream\n\t * @global\n\t * @public\n\t */\n\n\t/**\n\t * Converts the given token or token stream to an HTML representation.\n\t *\n\t * The following hooks will be run:\n\t * 1. `wrap`: On each {@link Token}.\n\t *\n\t * @param {string | Token | TokenStream} o The token or token stream to be converted.\n\t * @param {string} language The name of current language.\n\t * @returns {string} The HTML representation of the token or token stream.\n\t * @memberof Token\n\t * @static\n\t */\n\tToken.stringify = function stringify(o, language) {\n\t\tif (typeof o == 'string') {\n\t\t\treturn o;\n\t\t}\n\t\tif (Array.isArray(o)) {\n\t\t\tvar s = '';\n\t\t\to.forEach(function (e) {\n\t\t\t\ts += stringify(e, language);\n\t\t\t});\n\t\t\treturn s;\n\t\t}\n\n\t\tvar env = {\n\t\t\ttype: o.type,\n\t\t\tcontent: stringify(o.content, language),\n\t\t\ttag: 'span',\n\t\t\tclasses: ['token', o.type],\n\t\t\tattributes: {},\n\t\t\tlanguage: language\n\t\t};\n\n\t\tvar aliases = o.alias;\n\t\tif (aliases) {\n\t\t\tif (Array.isArray(aliases)) {\n\t\t\t\tArray.prototype.push.apply(env.classes, aliases);\n\t\t\t} else {\n\t\t\t\tenv.classes.push(aliases);\n\t\t\t}\n\t\t}\n\n\t\t_.hooks.run('wrap', env);\n\n\t\tvar attributes = '';\n\t\tfor (var name in env.attributes) {\n\t\t\tattributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n\t\t}\n\n\t\treturn '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n\t};\n\n\t/**\n\t * @param {RegExp} pattern\n\t * @param {number} pos\n\t * @param {string} text\n\t * @param {boolean} lookbehind\n\t * @returns {RegExpExecArray | null}\n\t */\n\tfunction matchPattern(pattern, pos, text, lookbehind) {\n\t\tpattern.lastIndex = pos;\n\t\tvar match = pattern.exec(text);\n\t\tif (match && lookbehind && match[1]) {\n\t\t\t// change the match to remove the text matched by the Prism lookbehind group\n\t\t\tvar lookbehindLength = match[1].length;\n\t\t\tmatch.index += lookbehindLength;\n\t\t\tmatch[0] = match[0].slice(lookbehindLength);\n\t\t}\n\t\treturn match;\n\t}\n\n\t/**\n\t * @param {string} text\n\t * @param {LinkedList<string | Token>} tokenList\n\t * @param {any} grammar\n\t * @param {LinkedListNode<string | Token>} startNode\n\t * @param {number} startPos\n\t * @param {RematchOptions} [rematch]\n\t * @returns {void}\n\t * @private\n\t *\n\t * @typedef RematchOptions\n\t * @property {string} cause\n\t * @property {number} reach\n\t */\n\tfunction matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n\t\tfor (var token in grammar) {\n\t\t\tif (!grammar.hasOwnProperty(token) || !grammar[token]) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tvar patterns = grammar[token];\n\t\t\tpatterns = Array.isArray(patterns) ? patterns : [patterns];\n\n\t\t\tfor (var j = 0; j < patterns.length; ++j) {\n\t\t\t\tif (rematch && rematch.cause == token + ',' + j) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar patternObj = patterns[j];\n\t\t\t\tvar inside = patternObj.inside;\n\t\t\t\tvar lookbehind = !!patternObj.lookbehind;\n\t\t\t\tvar greedy = !!patternObj.greedy;\n\t\t\t\tvar alias = patternObj.alias;\n\n\t\t\t\tif (greedy && !patternObj.pattern.global) {\n\t\t\t\t\t// Without the global flag, lastIndex won't work\n\t\t\t\t\tvar flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n\t\t\t\t\tpatternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n\t\t\t\t}\n\n\t\t\t\t/** @type {RegExp} */\n\t\t\t\tvar pattern = patternObj.pattern || patternObj;\n\n\t\t\t\tfor ( // iterate the token list and keep track of the current token/string position\n\t\t\t\t\tvar currentNode = startNode.next, pos = startPos;\n\t\t\t\t\tcurrentNode !== tokenList.tail;\n\t\t\t\t\tpos += currentNode.value.length, currentNode = currentNode.next\n\t\t\t\t) {\n\n\t\t\t\t\tif (rematch && pos >= rematch.reach) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar str = currentNode.value;\n\n\t\t\t\t\tif (tokenList.length > text.length) {\n\t\t\t\t\t\t// Something went terribly wrong, ABORT, ABORT!\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (str instanceof Token) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeCount = 1; // this is the to parameter of removeBetween\n\t\t\t\t\tvar match;\n\n\t\t\t\t\tif (greedy) {\n\t\t\t\t\t\tmatch = matchPattern(pattern, pos, text, lookbehind);\n\t\t\t\t\t\tif (!match || match.index >= text.length) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar from = match.index;\n\t\t\t\t\t\tvar to = match.index + match[0].length;\n\t\t\t\t\t\tvar p = pos;\n\n\t\t\t\t\t\t// find the node that contains the match\n\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\twhile (from >= p) {\n\t\t\t\t\t\t\tcurrentNode = currentNode.next;\n\t\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// adjust pos (and p)\n\t\t\t\t\t\tp -= currentNode.value.length;\n\t\t\t\t\t\tpos = p;\n\n\t\t\t\t\t\t// the current node is a Token, then the match starts inside another Token, which is invalid\n\t\t\t\t\t\tif (currentNode.value instanceof Token) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// find the last node which is affected by this match\n\t\t\t\t\t\tfor (\n\t\t\t\t\t\t\tvar k = currentNode;\n\t\t\t\t\t\t\tk !== tokenList.tail && (p < to || typeof k.value === 'string');\n\t\t\t\t\t\t\tk = k.next\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tremoveCount++;\n\t\t\t\t\t\t\tp += k.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tremoveCount--;\n\n\t\t\t\t\t\t// replace with the new match\n\t\t\t\t\t\tstr = text.slice(pos, p);\n\t\t\t\t\t\tmatch.index -= pos;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmatch = matchPattern(pattern, 0, str, lookbehind);\n\t\t\t\t\t\tif (!match) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// eslint-disable-next-line no-redeclare\n\t\t\t\t\tvar from = match.index;\n\t\t\t\t\tvar matchStr = match[0];\n\t\t\t\t\tvar before = str.slice(0, from);\n\t\t\t\t\tvar after = str.slice(from + matchStr.length);\n\n\t\t\t\t\tvar reach = pos + str.length;\n\t\t\t\t\tif (rematch && reach > rematch.reach) {\n\t\t\t\t\t\trematch.reach = reach;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeFrom = currentNode.prev;\n\n\t\t\t\t\tif (before) {\n\t\t\t\t\t\tremoveFrom = addAfter(tokenList, removeFrom, before);\n\t\t\t\t\t\tpos += before.length;\n\t\t\t\t\t}\n\n\t\t\t\t\tremoveRange(tokenList, removeFrom, removeCount);\n\n\t\t\t\t\tvar wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n\t\t\t\t\tcurrentNode = addAfter(tokenList, removeFrom, wrapped);\n\n\t\t\t\t\tif (after) {\n\t\t\t\t\t\taddAfter(tokenList, currentNode, after);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (removeCount > 1) {\n\t\t\t\t\t\t// at least one Token object was removed, so we have to do some rematching\n\t\t\t\t\t\t// this can only happen if the current pattern is greedy\n\n\t\t\t\t\t\t/** @type {RematchOptions} */\n\t\t\t\t\t\tvar nestedRematch = {\n\t\t\t\t\t\t\tcause: token + ',' + j,\n\t\t\t\t\t\t\treach: reach\n\t\t\t\t\t\t};\n\t\t\t\t\t\tmatchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n\t\t\t\t\t\t// the reach might have been extended because of the rematching\n\t\t\t\t\t\tif (rematch && nestedRematch.reach > rematch.reach) {\n\t\t\t\t\t\t\trematch.reach = nestedRematch.reach;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @typedef LinkedListNode\n\t * @property {T} value\n\t * @property {LinkedListNode<T> | null} prev The previous node.\n\t * @property {LinkedListNode<T> | null} next The next node.\n\t * @template T\n\t * @private\n\t */\n\n\t/**\n\t * @template T\n\t * @private\n\t */\n\tfunction LinkedList() {\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar head = { value: null, prev: null, next: null };\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar tail = { value: null, prev: head, next: null };\n\t\thead.next = tail;\n\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.head = head;\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.tail = tail;\n\t\tthis.length = 0;\n\t}\n\n\t/**\n\t * Adds a new node with the given value to the list.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {T} value\n\t * @returns {LinkedListNode<T>} The added node.\n\t * @template T\n\t */\n\tfunction addAfter(list, node, value) {\n\t\t// assumes that node != list.tail && values.length >= 0\n\t\tvar next = node.next;\n\n\t\tvar newNode = { value: value, prev: node, next: next };\n\t\tnode.next = newNode;\n\t\tnext.prev = newNode;\n\t\tlist.length++;\n\n\t\treturn newNode;\n\t}\n\t/**\n\t * Removes `count` nodes after the given node. The given node will not be removed.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {number} count\n\t * @template T\n\t */\n\tfunction removeRange(list, node, count) {\n\t\tvar next = node.next;\n\t\tfor (var i = 0; i < count && next !== list.tail; i++) {\n\t\t\tnext = next.next;\n\t\t}\n\t\tnode.next = next;\n\t\tnext.prev = node;\n\t\tlist.length -= i;\n\t}\n\t/**\n\t * @param {LinkedList<T>} list\n\t * @returns {T[]}\n\t * @template T\n\t */\n\tfunction toArray(list) {\n\t\tvar array = [];\n\t\tvar node = list.head.next;\n\t\twhile (node !== list.tail) {\n\t\t\tarray.push(node.value);\n\t\t\tnode = node.next;\n\t\t}\n\t\treturn array;\n\t}\n\treturn _;\n}());\n\nmodule.exports = Prism;\nPrism.default = Prism;\n", "import { Prism } from \"./prism-langs\"\nimport * as themes from \"./themes\"\nimport { createElement } from \"react\"\nimport { Highlight as InternalHighlight } from \"./components/highlight\"\nimport { HighlightProps, PrismLib } from \"./types\"\nimport normalizeTokens from \"./utils/normalizeTokens\"\nimport { useTokenize } from \"./components/useTokenize\"\nexport * from \"./types\"\n\n/**\n * Prism React Renderer requires this specific instance\n * of Prism provided to ensure the languages are correctly loaded\n */\nconst Highlight = (props: HighlightProps) =>\n  createElement(InternalHighlight, {\n    ...props,\n    prism: props.prism || (Prism as PrismLib),\n    theme: props.theme || themes.vsDark,\n    code: props.code,\n    language: props.language,\n  })\n\nexport { Highlight, Prism, themes, normalizeTokens, useTokenize }\n", "// eslint-disable-next-line @typescript-eslint/ban-ts-comment\n// @ts-nocheck\nimport * as Prism from \"prismjs\";\nexport { Prism };Prism.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\\s\\S])*?-->/,greedy:!0},prolog:{pattern:/<\\?[\\s\\S]+?\\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,greedy:!0,inside:{\"internal-subset\":{pattern:/(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/\"[^\"]*\"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\\]]/,\"doctype-tag\":/^DOCTYPE/i,name:/[^\\s<>'\"]+/}},cdata:{pattern:/<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,greedy:!0},tag:{pattern:/<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,greedy:!0,inside:{tag:{pattern:/^<\\/?[^\\s>\\/]+/,inside:{punctuation:/^<\\/?/,namespace:/^[^\\s>\\/:]+:/}},\"special-attr\":[],\"attr-value\":{pattern:/=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:\"attr-equals\"},{pattern:/^(\\s*)[\"']|[\"']$/,lookbehind:!0}]}},punctuation:/\\/?>/,\"attr-name\":{pattern:/[^\\s>\\/]+/,inside:{namespace:/^[^\\s>\\/:]+:/}}}},entity:[{pattern:/&[\\da-z]{1,8};/i,alias:\"named-entity\"},/&#x?[\\da-f]{1,8};/i]},Prism.languages.markup.tag.inside[\"attr-value\"].inside.entity=Prism.languages.markup.entity,Prism.languages.markup.doctype.inside[\"internal-subset\"].inside=Prism.languages.markup,Prism.hooks.add(\"wrap\",function(e){\"entity\"===e.type&&(e.attributes.title=e.content.replace(/&amp;/,\"&\"))}),Object.defineProperty(Prism.languages.markup.tag,\"addInlined\",{value:function(e,n){var t={},t=(t[\"language-\"+n]={pattern:/(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,lookbehind:!0,inside:Prism.languages[n]},t.cdata=/^<!\\[CDATA\\[|\\]\\]>$/i,{\"included-cdata\":{pattern:/<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,inside:t}}),n=(t[\"language-\"+n]={pattern:/[\\s\\S]+/,inside:Prism.languages[n]},{});n[e]={pattern:RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g,function(){return e}),\"i\"),lookbehind:!0,greedy:!0,inside:t},Prism.languages.insertBefore(\"markup\",\"cdata\",n)}}),Object.defineProperty(Prism.languages.markup.tag,\"addAttribute\",{value:function(e,n){Prism.languages.markup.tag.inside[\"special-attr\"].push({pattern:RegExp(/(^|[\"'\\s])/.source+\"(?:\"+e+\")\"+/\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\"i\"),lookbehind:!0,inside:{\"attr-name\":/^[^\\s=]+/,\"attr-value\":{pattern:/=[\\s\\S]+/,inside:{value:{pattern:/(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,lookbehind:!0,alias:[n,\"language-\"+n],inside:Prism.languages[n]},punctuation:[{pattern:/^=/,alias:\"attr-equals\"},/\"|'/]}}}})}}),Prism.languages.html=Prism.languages.markup,Prism.languages.mathml=Prism.languages.markup,Prism.languages.svg=Prism.languages.markup,Prism.languages.xml=Prism.languages.extend(\"markup\",{}),Prism.languages.ssml=Prism.languages.xml,Prism.languages.atom=Prism.languages.xml,Prism.languages.rss=Prism.languages.xml,function(e){var n={pattern:/\\\\[\\\\(){}[\\]^$+*?|.]/,alias:\"escape\"},t=/\\\\(?:x[\\da-fA-F]{2}|u[\\da-fA-F]{4}|u\\{[\\da-fA-F]+\\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/,a=\"(?:[^\\\\\\\\-]|\"+t.source+\")\",a=RegExp(a+\"-\"+a),r={pattern:/(<|')[^<>']+(?=[>']$)/,lookbehind:!0,alias:\"variable\"};e.languages.regex={\"char-class\":{pattern:/((?:^|[^\\\\])(?:\\\\\\\\)*)\\[(?:[^\\\\\\]]|\\\\[\\s\\S])*\\]/,lookbehind:!0,inside:{\"char-class-negation\":{pattern:/(^\\[)\\^/,lookbehind:!0,alias:\"operator\"},\"char-class-punctuation\":{pattern:/^\\[|\\]$/,alias:\"punctuation\"},range:{pattern:a,inside:{escape:t,\"range-punctuation\":{pattern:/-/,alias:\"operator\"}}},\"special-escape\":n,\"char-set\":{pattern:/\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,alias:\"class-name\"},escape:t}},\"special-escape\":n,\"char-set\":{pattern:/\\.|\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,alias:\"class-name\"},backreference:[{pattern:/\\\\(?![123][0-7]{2})[1-9]/,alias:\"keyword\"},{pattern:/\\\\k<[^<>']+>/,alias:\"keyword\",inside:{\"group-name\":r}}],anchor:{pattern:/[$^]|\\\\[ABbGZz]/,alias:\"function\"},escape:t,group:[{pattern:/\\((?:\\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,alias:\"punctuation\",inside:{\"group-name\":r}},{pattern:/\\)/,alias:\"punctuation\"}],quantifier:{pattern:/(?:[+*?]|\\{\\d+(?:,\\d*)?\\})[?+]?/,alias:\"number\"},alternation:{pattern:/\\|/,alias:\"keyword\"}}}(Prism),Prism.languages.clike={comment:[{pattern:/(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\\\:])\\/\\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,greedy:!0},\"class-name\":{pattern:/(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\\\]/}},keyword:/\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,boolean:/\\b(?:false|true)\\b/,function:/\\b\\w+(?=\\()/,number:/\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,punctuation:/[{}[\\];(),.:]/},Prism.languages.javascript=Prism.languages.extend(\"clike\",{\"class-name\":[Prism.languages.clike[\"class-name\"],{pattern:/(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\\})\\s*)catch\\b/,lookbehind:!0},{pattern:/(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,lookbehind:!0}],function:/#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,number:{pattern:RegExp(/(^|[^\\w$])/.source+\"(?:\"+/NaN|Infinity/.source+\"|\"+/0[bB][01]+(?:_[01]+)*n?/.source+\"|\"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+\"|\"+/0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source+\"|\"+/\\d+(?:_\\d+)*n/.source+\"|\"+/(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source+\")\"+/(?![\\w$])/.source),lookbehind:!0},operator:/--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/}),Prism.languages.javascript[\"class-name\"][0].pattern=/(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/,Prism.languages.insertBefore(\"javascript\",\"keyword\",{regex:{pattern:RegExp(/((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/.source+/\\//.source+\"(?:\"+/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}/.source+\"|\"+/(?:\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+\")\"+/(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/.source),lookbehind:!0,greedy:!0,inside:{\"regex-source\":{pattern:/^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,lookbehind:!0,alias:\"language-regex\",inside:Prism.languages.regex},\"regex-delimiter\":/^\\/|\\/$/,\"regex-flags\":/^[a-z]+$/}},\"function-variable\":{pattern:/#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,alias:\"function\"},parameter:[{pattern:/(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,lookbehind:!0,inside:Prism.languages.javascript}],constant:/\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/}),Prism.languages.insertBefore(\"javascript\",\"string\",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:\"comment\"},\"template-string\":{pattern:/`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,greedy:!0,inside:{\"template-punctuation\":{pattern:/^`|`$/,alias:\"string\"},interpolation:{pattern:/((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,lookbehind:!0,inside:{\"interpolation-punctuation\":{pattern:/^\\$\\{|\\}$/,alias:\"punctuation\"},rest:Prism.languages.javascript}},string:/[\\s\\S]+/}},\"string-property\":{pattern:/((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,lookbehind:!0,greedy:!0,alias:\"property\"}}),Prism.languages.insertBefore(\"javascript\",\"operator\",{\"literal-property\":{pattern:/((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,lookbehind:!0,alias:\"property\"}}),Prism.languages.markup&&(Prism.languages.markup.tag.addInlined(\"script\",\"javascript\"),Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,\"javascript\")),Prism.languages.js=Prism.languages.javascript,Prism.languages.actionscript=Prism.languages.extend(\"javascript\",{keyword:/\\b(?:as|break|case|catch|class|const|default|delete|do|dynamic|each|else|extends|final|finally|for|function|get|if|implements|import|in|include|instanceof|interface|internal|is|namespace|native|new|null|override|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|use|var|void|while|with)\\b/,operator:/\\+\\+|--|(?:[+\\-*\\/%^]|&&?|\\|\\|?|<<?|>>?>?|[!=]=?)=?|[~?@]/}),Prism.languages.actionscript[\"class-name\"].alias=\"function\",delete Prism.languages.actionscript.parameter,delete Prism.languages.actionscript[\"literal-property\"],Prism.languages.markup&&Prism.languages.insertBefore(\"actionscript\",\"string\",{xml:{pattern:/(^|[^.])<\\/?\\w+(?:\\s+[^\\s>\\/=]+=(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2)*\\s*\\/?>/,lookbehind:!0,inside:Prism.languages.markup}}),function(e){var n=/#(?!\\{).+/,t={pattern:/#\\{[^}]+\\}/,alias:\"variable\"};e.languages.coffeescript=e.languages.extend(\"javascript\",{comment:n,string:[{pattern:/'(?:\\\\[\\s\\S]|[^\\\\'])*'/,greedy:!0},{pattern:/\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,greedy:!0,inside:{interpolation:t}}],keyword:/\\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\\b/,\"class-member\":{pattern:/@(?!\\d)\\w+/,alias:\"variable\"}}),e.languages.insertBefore(\"coffeescript\",\"comment\",{\"multiline-comment\":{pattern:/###[\\s\\S]+?###/,alias:\"comment\"},\"block-regex\":{pattern:/\\/{3}[\\s\\S]*?\\/{3}/,alias:\"regex\",inside:{comment:n,interpolation:t}}}),e.languages.insertBefore(\"coffeescript\",\"string\",{\"inline-javascript\":{pattern:/`(?:\\\\[\\s\\S]|[^\\\\`])*`/,inside:{delimiter:{pattern:/^`|`$/,alias:\"punctuation\"},script:{pattern:/[\\s\\S]+/,alias:\"language-javascript\",inside:e.languages.javascript}}},\"multiline-string\":[{pattern:/'''[\\s\\S]*?'''/,greedy:!0,alias:\"string\"},{pattern:/\"\"\"[\\s\\S]*?\"\"\"/,greedy:!0,alias:\"string\",inside:{interpolation:t}}]}),e.languages.insertBefore(\"coffeescript\",\"keyword\",{property:/(?!\\d)\\w+(?=\\s*:(?!:))/}),delete e.languages.coffeescript[\"template-string\"],e.languages.coffee=e.languages.coffeescript}(Prism),function(l){var e=l.languages.javadoclike={parameter:{pattern:/(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*@(?:arg|arguments|param)\\s+)\\w+/m,lookbehind:!0},keyword:{pattern:/(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*|\\{)@[a-z][a-zA-Z-]+\\b/m,lookbehind:!0},punctuation:/[{}]/};Object.defineProperty(e,\"addSupport\",{value:function(e,o){(e=\"string\"==typeof e?[e]:e).forEach(function(e){var n=function(e){e.inside||(e.inside={}),e.inside.rest=o},t=\"doc-comment\";if(a=l.languages[e]){var a,r=a[t];if((r=r?r:(a=l.languages.insertBefore(e,\"comment\",{\"doc-comment\":{pattern:/(^|[^\\\\])\\/\\*\\*[^/][\\s\\S]*?(?:\\*\\/|$)/,lookbehind:!0,alias:\"comment\"}}))[t])instanceof RegExp&&(r=a[t]={pattern:r}),Array.isArray(r))for(var s=0,i=r.length;s<i;s++)r[s]instanceof RegExp&&(r[s]={pattern:r[s]}),n(r[s]);else n(r)}})}}),e.addSupport([\"java\",\"javascript\",\"php\"],e)}(Prism),function(e){var n=/(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/,n=(e.languages.css={comment:/\\/\\*[\\s\\S]*?\\*\\//,atrule:{pattern:RegExp(\"@[\\\\w-](?:\"+/[^;{\\s\"']|\\s+(?!\\s)/.source+\"|\"+n.source+\")*?\"+/(?:;|(?=\\s*\\{))/.source),inside:{rule:/^@[\\w-]+/,\"selector-function-argument\":{pattern:/(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,lookbehind:!0,alias:\"selector\"},keyword:{pattern:/(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,lookbehind:!0}}},url:{pattern:RegExp(\"\\\\burl\\\\((?:\"+n.source+\"|\"+/(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source+\")\\\\)\",\"i\"),greedy:!0,inside:{function:/^url/i,punctuation:/^\\(|\\)$/,string:{pattern:RegExp(\"^\"+n.source+\"$\"),alias:\"url\"}}},selector:{pattern:RegExp(\"(^|[{}\\\\s])[^{}\\\\s](?:[^{};\\\"'\\\\s]|\\\\s+(?![\\\\s{])|\"+n.source+\")*(?=\\\\s*\\\\{)\"),lookbehind:!0},string:{pattern:n,greedy:!0},property:{pattern:/(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,lookbehind:!0},important:/!important\\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css,e.languages.markup);n&&(n.tag.addInlined(\"style\",\"css\"),n.tag.addAttribute(\"style\",\"css\"))}(Prism),function(e){var n=/(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,n=(e.languages.css.selector={pattern:e.languages.css.selector.pattern,lookbehind:!0,inside:n={\"pseudo-element\":/:(?:after|before|first-letter|first-line|selection)|::[-\\w]+/,\"pseudo-class\":/:[-\\w]+/,class:/\\.[-\\w]+/,id:/#[-\\w]+/,attribute:{pattern:RegExp(\"\\\\[(?:[^[\\\\]\\\"']|\"+n.source+\")*\\\\]\"),greedy:!0,inside:{punctuation:/^\\[|\\]$/,\"case-sensitivity\":{pattern:/(\\s)[si]$/i,lookbehind:!0,alias:\"keyword\"},namespace:{pattern:/^(\\s*)(?:(?!\\s)[-*\\w\\xA0-\\uFFFF])*\\|(?!=)/,lookbehind:!0,inside:{punctuation:/\\|$/}},\"attr-name\":{pattern:/^(\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+/,lookbehind:!0},\"attr-value\":[n,{pattern:/(=\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+(?=\\s*$)/,lookbehind:!0}],operator:/[|~*^$]?=/}},\"n-th\":[{pattern:/(\\(\\s*)[+-]?\\d*[\\dn](?:\\s*[+-]\\s*\\d+)?(?=\\s*\\))/,lookbehind:!0,inside:{number:/[\\dn]+/,operator:/[+-]/}},{pattern:/(\\(\\s*)(?:even|odd)(?=\\s*\\))/i,lookbehind:!0}],combinator:/>|\\+|~|\\|\\|/,punctuation:/[(),]/}},e.languages.css.atrule.inside[\"selector-function-argument\"].inside=n,e.languages.insertBefore(\"css\",\"property\",{variable:{pattern:/(^|[^-\\w\\xA0-\\uFFFF])--(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*/i,lookbehind:!0}}),{pattern:/(\\b\\d+)(?:%|[a-z]+(?![\\w-]))/,lookbehind:!0}),t={pattern:/(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/,lookbehind:!0};e.languages.insertBefore(\"css\",\"function\",{operator:{pattern:/(\\s)[+\\-*\\/](?=\\s)/,lookbehind:!0},hexcode:{pattern:/\\B#[\\da-f]{3,8}\\b/i,alias:\"color\"},color:[{pattern:/(^|[^\\w-])(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|RebeccaPurple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)(?![\\w-])/i,lookbehind:!0},{pattern:/\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i,inside:{unit:n,number:t,function:/[\\w-]+(?=\\()/,punctuation:/[(),]/}}],entity:/\\\\[\\da-f]{1,8}/i,unit:n,number:t})}(Prism),function(e){var n=/[*&][^\\s[\\]{},]+/,t=/!(?:<[\\w\\-%#;/?:@&=+$,.!~*'()[\\]]+>|(?:[a-zA-Z\\d-]*!)?[\\w\\-%#;/?:@&=+$.~*'()]+)?/,a=\"(?:\"+t.source+\"(?:[ \\t]+\"+n.source+\")?|\"+n.source+\"(?:[ \\t]+\"+t.source+\")?)\",r=/(?:[^\\s\\x00-\\x08\\x0e-\\x1f!\"#%&'*,\\-:>?@[\\]`{|}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]|[?:-]<PLAIN>)(?:[ \\t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g,function(){return/[^\\s\\x00-\\x08\\x0e-\\x1f,[\\]{}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]/.source}),s=/\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/.source;function i(e,n){n=(n||\"\").replace(/m/g,\"\")+\"m\";var t=/([:\\-,[{]\\s*(?:\\s<<prop>>[ \\t]+)?)(?:<<value>>)(?=[ \\t]*(?:$|,|\\]|\\}|(?:[\\r\\n]\\s*)?#))/.source.replace(/<<prop>>/g,function(){return a}).replace(/<<value>>/g,function(){return e});return RegExp(t,n)}e.languages.yaml={scalar:{pattern:RegExp(/([\\-:]\\s*(?:\\s<<prop>>[ \\t]+)?[|>])[ \\t]*(?:((?:\\r?\\n|\\r)[ \\t]+)\\S[^\\r\\n]*(?:\\2[^\\r\\n]+)*)/.source.replace(/<<prop>>/g,function(){return a})),lookbehind:!0,alias:\"string\"},comment:/#.*/,key:{pattern:RegExp(/((?:^|[:\\-,[{\\r\\n?])[ \\t]*(?:<<prop>>[ \\t]+)?)<<key>>(?=\\s*:\\s)/.source.replace(/<<prop>>/g,function(){return a}).replace(/<<key>>/g,function(){return\"(?:\"+r+\"|\"+s+\")\"})),lookbehind:!0,greedy:!0,alias:\"atrule\"},directive:{pattern:/(^[ \\t]*)%.+/m,lookbehind:!0,alias:\"important\"},datetime:{pattern:i(/\\d{4}-\\d\\d?-\\d\\d?(?:[tT]|[ \\t]+)\\d\\d?:\\d{2}:\\d{2}(?:\\.\\d*)?(?:[ \\t]*(?:Z|[-+]\\d\\d?(?::\\d{2})?))?|\\d{4}-\\d{2}-\\d{2}|\\d\\d?:\\d{2}(?::\\d{2}(?:\\.\\d*)?)?/.source),lookbehind:!0,alias:\"number\"},boolean:{pattern:i(/false|true/.source,\"i\"),lookbehind:!0,alias:\"important\"},null:{pattern:i(/null|~/.source,\"i\"),lookbehind:!0,alias:\"important\"},string:{pattern:i(s),lookbehind:!0,greedy:!0},number:{pattern:i(/[+-]?(?:0x[\\da-f]+|0o[0-7]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|\\.inf|\\.nan)/.source,\"i\"),lookbehind:!0},tag:t,important:n,punctuation:/---|[:[\\]{}\\-,|>?]|\\.\\.\\./},e.languages.yml=e.languages.yaml}(Prism),function(o){var n=/(?:\\\\.|[^\\\\\\n\\r]|(?:\\n|\\r\\n?)(?![\\r\\n]))/.source;function e(e){return e=e.replace(/<inner>/g,function(){return n}),RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source+\"(?:\"+e+\")\")}var t=/(?:\\\\.|``(?:[^`\\r\\n]|`(?!`))+``|`[^`\\r\\n]+`|[^\\\\|\\r\\n`])+/.source,a=/\\|?__(?:\\|__)+\\|?(?:(?:\\n|\\r\\n?)|(?![\\s\\S]))/.source.replace(/__/g,function(){return t}),r=/\\|?[ \\t]*:?-{3,}:?[ \\t]*(?:\\|[ \\t]*:?-{3,}:?[ \\t]*)+\\|?(?:\\n|\\r\\n?)/.source,l=(o.languages.markdown=o.languages.extend(\"markup\",{}),o.languages.insertBefore(\"markdown\",\"prolog\",{\"front-matter-block\":{pattern:/(^(?:\\s*[\\r\\n])?)---(?!.)[\\s\\S]*?[\\r\\n]---(?!.)/,lookbehind:!0,greedy:!0,inside:{punctuation:/^---|---$/,\"front-matter\":{pattern:/\\S+(?:\\s+\\S+)*/,alias:[\"yaml\",\"language-yaml\"],inside:o.languages.yaml}}},blockquote:{pattern:/^>(?:[\\t ]*>)*/m,alias:\"punctuation\"},table:{pattern:RegExp(\"^\"+a+r+\"(?:\"+a+\")*\",\"m\"),inside:{\"table-data-rows\":{pattern:RegExp(\"^(\"+a+r+\")(?:\"+a+\")*$\"),lookbehind:!0,inside:{\"table-data\":{pattern:RegExp(t),inside:o.languages.markdown},punctuation:/\\|/}},\"table-line\":{pattern:RegExp(\"^(\"+a+\")\"+r+\"$\"),lookbehind:!0,inside:{punctuation:/\\||:?-{3,}:?/}},\"table-header-row\":{pattern:RegExp(\"^\"+a+\"$\"),inside:{\"table-header\":{pattern:RegExp(t),alias:\"important\",inside:o.languages.markdown},punctuation:/\\|/}}}},code:[{pattern:/((?:^|\\n)[ \\t]*\\n|(?:^|\\r\\n?)[ \\t]*\\r\\n?)(?: {4}|\\t).+(?:(?:\\n|\\r\\n?)(?: {4}|\\t).+)*/,lookbehind:!0,alias:\"keyword\"},{pattern:/^```[\\s\\S]*?^```$/m,greedy:!0,inside:{\"code-block\":{pattern:/^(```.*(?:\\n|\\r\\n?))[\\s\\S]+?(?=(?:\\n|\\r\\n?)^```$)/m,lookbehind:!0},\"code-language\":{pattern:/^(```).+/,lookbehind:!0},punctuation:/```/}}],title:[{pattern:/\\S.*(?:\\n|\\r\\n?)(?:==+|--+)(?=[ \\t]*$)/m,alias:\"important\",inside:{punctuation:/==+$|--+$/}},{pattern:/(^\\s*)#.+/m,lookbehind:!0,alias:\"important\",inside:{punctuation:/^#+|#+$/}}],hr:{pattern:/(^\\s*)([*-])(?:[\\t ]*\\2){2,}(?=\\s*$)/m,lookbehind:!0,alias:\"punctuation\"},list:{pattern:/(^\\s*)(?:[*+-]|\\d+\\.)(?=[\\t ].)/m,lookbehind:!0,alias:\"punctuation\"},\"url-reference\":{pattern:/!?\\[[^\\]]+\\]:[\\t ]+(?:\\S+|<(?:\\\\.|[^>\\\\])+>)(?:[\\t ]+(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\)))?/,inside:{variable:{pattern:/^(!?\\[)[^\\]]+/,lookbehind:!0},string:/(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\))$/,punctuation:/^[\\[\\]!:]|[<>]/},alias:\"url\"},bold:{pattern:e(/\\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\\b|\\*\\*(?:(?!\\*)<inner>|\\*(?:(?!\\*)<inner>)+\\*)+\\*\\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^..)[\\s\\S]+(?=..$)/,lookbehind:!0,inside:{}},punctuation:/\\*\\*|__/}},italic:{pattern:e(/\\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\\b|\\*(?:(?!\\*)<inner>|\\*\\*(?:(?!\\*)<inner>)+\\*\\*)+\\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^.)[\\s\\S]+(?=.$)/,lookbehind:!0,inside:{}},punctuation:/[*_]/}},strike:{pattern:e(/(~~?)(?:(?!~)<inner>)+\\2/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^~~?)[\\s\\S]+(?=\\1$)/,lookbehind:!0,inside:{}},punctuation:/~~?/}},\"code-snippet\":{pattern:/(^|[^\\\\`])(?:``[^`\\r\\n]+(?:`[^`\\r\\n]+)*``(?!`)|`[^`\\r\\n]+`(?!`))/,lookbehind:!0,greedy:!0,alias:[\"code\",\"keyword\"]},url:{pattern:e(/!?\\[(?:(?!\\])<inner>)+\\](?:\\([^\\s)]+(?:[\\t ]+\"(?:\\\\.|[^\"\\\\])*\")?\\)|[ \\t]?\\[(?:(?!\\])<inner>)+\\])/.source),lookbehind:!0,greedy:!0,inside:{operator:/^!/,content:{pattern:/(^\\[)[^\\]]+(?=\\])/,lookbehind:!0,inside:{}},variable:{pattern:/(^\\][ \\t]?\\[)[^\\]]+(?=\\]$)/,lookbehind:!0},url:{pattern:/(^\\]\\()[^\\s)]+/,lookbehind:!0},string:{pattern:/(^[ \\t]+)\"(?:\\\\.|[^\"\\\\])*\"(?=\\)$)/,lookbehind:!0}}}}),[\"url\",\"bold\",\"italic\",\"strike\"].forEach(function(n){[\"url\",\"bold\",\"italic\",\"strike\",\"code-snippet\"].forEach(function(e){n!==e&&(o.languages.markdown[n].inside.content.inside[e]=o.languages.markdown[e])})}),o.hooks.add(\"after-tokenize\",function(e){\"markdown\"!==e.language&&\"md\"!==e.language||!function e(n){if(n&&\"string\"!=typeof n)for(var t=0,a=n.length;t<a;t++){var r,s=n[t];\"code\"!==s.type?e(s.content):(r=s.content[1],s=s.content[3],r&&s&&\"code-language\"===r.type&&\"code-block\"===s.type&&\"string\"==typeof r.content&&(r=r.content.replace(/\\b#/g,\"sharp\").replace(/\\b\\+\\+/g,\"pp\"),r=\"language-\"+(r=(/[a-z][\\w-]*/i.exec(r)||[\"\"])[0].toLowerCase()),s.alias?\"string\"==typeof s.alias?s.alias=[s.alias,r]:s.alias.push(r):s.alias=[r]))}}(e.tokens)}),o.hooks.add(\"wrap\",function(e){if(\"code-block\"===e.type){for(var n=\"\",t=0,a=e.classes.length;t<a;t++){var r=e.classes[t],r=/language-(.+)/.exec(r);if(r){n=r[1];break}}var s,i=o.languages[n];i?e.content=o.highlight(function(e){e=e.replace(l,\"\");return e=e.replace(/&(\\w{1,8}|#x?[\\da-f]{1,8});/gi,function(e,n){var t;return\"#\"===(n=n.toLowerCase())[0]?(t=\"x\"===n[1]?parseInt(n.slice(2),16):Number(n.slice(1)),c(t)):u[n]||e})}(e.content),i,n):n&&\"none\"!==n&&o.plugins.autoloader&&(s=\"md-\"+(new Date).valueOf()+\"-\"+Math.floor(1e16*Math.random()),e.attributes.id=s,o.plugins.autoloader.loadLanguages(n,function(){var e=document.getElementById(s);e&&(e.innerHTML=o.highlight(e.textContent,o.languages[n],n))}))}}),RegExp(o.languages.markup.tag.pattern.source,\"gi\")),u={amp:\"&\",lt:\"<\",gt:\">\",quot:'\"'},c=String.fromCodePoint||String.fromCharCode;o.languages.md=o.languages.markdown}(Prism),Prism.languages.graphql={comment:/#.*/,description:{pattern:/(?:\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")(?=\\s*[a-z_])/i,greedy:!0,alias:\"string\",inside:{\"language-markdown\":{pattern:/(^\"(?:\"\")?)(?!\\1)[\\s\\S]+(?=\\1$)/,lookbehind:!0,inside:Prism.languages.markdown}}},string:{pattern:/\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,greedy:!0},number:/(?:\\B-|\\b)\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,boolean:/\\b(?:false|true)\\b/,variable:/\\$[a-z_]\\w*/i,directive:{pattern:/@[a-z_]\\w*/i,alias:\"function\"},\"attr-name\":{pattern:/\\b[a-z_]\\w*(?=\\s*(?:\\((?:[^()\"]|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")*\\))?:)/i,greedy:!0},\"atom-input\":{pattern:/\\b[A-Z]\\w*Input\\b/,alias:\"class-name\"},scalar:/\\b(?:Boolean|Float|ID|Int|String)\\b/,constant:/\\b[A-Z][A-Z_\\d]*\\b/,\"class-name\":{pattern:/(\\b(?:enum|implements|interface|on|scalar|type|union)\\s+|&\\s*|:\\s*|\\[)[A-Z_]\\w*/,lookbehind:!0},fragment:{pattern:/(\\bfragment\\s+|\\.{3}\\s*(?!on\\b))[a-zA-Z_]\\w*/,lookbehind:!0,alias:\"function\"},\"definition-mutation\":{pattern:/(\\bmutation\\s+)[a-zA-Z_]\\w*/,lookbehind:!0,alias:\"function\"},\"definition-query\":{pattern:/(\\bquery\\s+)[a-zA-Z_]\\w*/,lookbehind:!0,alias:\"function\"},keyword:/\\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\\b/,operator:/[!=|&]|\\.{3}/,\"property-query\":/\\w+(?=\\s*\\()/,object:/\\w+(?=\\s*\\{)/,punctuation:/[!(){}\\[\\]:=,]/,property:/\\w+/},Prism.hooks.add(\"after-tokenize\",function(e){if(\"graphql\"===e.language)for(var i=e.tokens.filter(function(e){return\"string\"!=typeof e&&\"comment\"!==e.type&&\"scalar\"!==e.type}),o=0;o<i.length;){var n=i[o++];if(\"keyword\"===n.type&&\"mutation\"===n.content){var t=[];if(p([\"definition-mutation\",\"punctuation\"])&&\"(\"===c(1).content){o+=2;var a=d(/^\\($/,/^\\)$/);if(-1===a)continue;for(;o<a;o++){var r=c(0);\"variable\"===r.type&&(g(r,\"variable-input\"),t.push(r.content))}o=a+1}if(p([\"punctuation\",\"property-query\"])&&\"{\"===c(0).content&&(o++,g(c(0),\"property-mutation\"),0<t.length)){var s=d(/^\\{$/,/^\\}$/);if(-1!==s)for(var l=o;l<s;l++){var u=i[l];\"variable\"===u.type&&0<=t.indexOf(u.content)&&g(u,\"variable-input\")}}}}function c(e){return i[o+e]}function p(e,n){n=n||0;for(var t=0;t<e.length;t++){var a=c(t+n);if(!a||a.type!==e[t])return}return 1}function d(e,n){for(var t=1,a=o;a<i.length;a++){var r=i[a],s=r.content;if(\"punctuation\"===r.type&&\"string\"==typeof s)if(e.test(s))t++;else if(n.test(s)&&0===--t)return a}return-1}function g(e,n){var t=e.alias;t?Array.isArray(t)||(e.alias=t=[t]):e.alias=t=[],t.push(n)}}),Prism.languages.sql={comment:{pattern:/(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/|#).*)/,lookbehind:!0},variable:[{pattern:/@([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1/,greedy:!0},/@[\\w.$]+/],string:{pattern:/(^|[^@\\\\])(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\]|\\2\\2)*\\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\\\])`(?:\\\\[\\s\\S]|[^`\\\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\\s*\\()/i,keyword:/\\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\\b/i,boolean:/\\b(?:FALSE|NULL|TRUE)\\b/i,number:/\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+\\b/i,operator:/[-+*\\/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\\b/i,punctuation:/[;[\\]()`,.]/},function(b){var e=b.languages.javascript[\"template-string\"],t=e.pattern.source,m=e.inside.interpolation,f=m.inside[\"interpolation-punctuation\"],s=m.pattern.source;function n(e,n){if(b.languages[e])return{pattern:RegExp(\"((?:\"+n+\")\\\\s*)\"+t),lookbehind:!0,greedy:!0,inside:{\"template-punctuation\":{pattern:/^`|`$/,alias:\"string\"},\"embedded-code\":{pattern:/[\\s\\S]+/,alias:e}}}}function h(e,n,t){e={code:e,grammar:n,language:t};return b.hooks.run(\"before-tokenize\",e),e.tokens=b.tokenize(e.code,e.grammar),b.hooks.run(\"after-tokenize\",e),e.tokens}function l(a,e,r){var n=b.tokenize(a,{interpolation:{pattern:RegExp(s),lookbehind:!0}}),p=0,d={},n=h(n.map(function(e){if(\"string\"==typeof e)return e;for(var n,t,e=e.content;-1!==a.indexOf((t=p++,n=\"___\"+r.toUpperCase()+\"_\"+t+\"___\")););return d[n]=e,n}).join(\"\"),e,r),g=Object.keys(d);return p=0,function e(n){for(var t=0;t<n.length;t++){if(p>=g.length)return;var a,r,s,i,o,l,u,c=n[t];\"string\"==typeof c||\"string\"==typeof c.content?(a=g[p],-1!==(u=(l=\"string\"==typeof c?c:c.content).indexOf(a))&&(++p,r=l.substring(0,u),o=d[a],s=void 0,(i={})[\"interpolation-punctuation\"]=f,3===(i=b.tokenize(o,i)).length&&((s=[1,1]).push.apply(s,h(i[1],b.languages.javascript,\"javascript\")),i.splice.apply(i,s)),s=new b.Token(\"interpolation\",i,m.alias,o),i=l.substring(u+a.length),o=[],r&&o.push(r),o.push(s),i&&(e(l=[i]),o.push.apply(o,l)),\"string\"==typeof c?(n.splice.apply(n,[t,1].concat(o)),t+=o.length-1):c.content=o)):(u=c.content,Array.isArray(u)?e(u):e([u]))}}(n),new b.Token(r,n,\"language-\"+r,a)}b.languages.javascript[\"template-string\"]=[n(\"css\",/\\b(?:styled(?:\\([^)]*\\))?(?:\\s*\\.\\s*\\w+(?:\\([^)]*\\))*)*|css(?:\\s*\\.\\s*(?:global|resolve))?|createGlobalStyle|keyframes)/.source),n(\"html\",/\\bhtml|\\.\\s*(?:inner|outer)HTML\\s*\\+?=/.source),n(\"svg\",/\\bsvg/.source),n(\"markdown\",/\\b(?:markdown|md)/.source),n(\"graphql\",/\\b(?:gql|graphql(?:\\s*\\.\\s*experimental)?)/.source),n(\"sql\",/\\bsql/.source),e].filter(Boolean);var a={javascript:!0,js:!0,typescript:!0,ts:!0,jsx:!0,tsx:!0};function u(e){return\"string\"==typeof e?e:Array.isArray(e)?e.map(u).join(\"\"):u(e.content)}b.hooks.add(\"after-tokenize\",function(e){e.language in a&&!function e(n){for(var t=0,a=n.length;t<a;t++){var r,s,i,o=n[t];\"string\"!=typeof o&&(r=o.content,Array.isArray(r)?\"template-string\"===o.type?(o=r[1],3===r.length&&\"string\"!=typeof o&&\"embedded-code\"===o.type&&(s=u(o),o=o.alias,o=Array.isArray(o)?o[0]:o,i=b.languages[o])&&(r[1]=l(s,i,o))):e(r):\"string\"!=typeof r&&e([r]))}}(e.tokens)})}(Prism),function(e){e.languages.typescript=e.languages.extend(\"javascript\",{\"class-name\":{pattern:/(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/}),e.languages.typescript.keyword.push(/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/,/\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/,/\\btype\\b(?=\\s*(?:[\\{*]|$))/),delete e.languages.typescript.parameter,delete e.languages.typescript[\"literal-property\"];var n=e.languages.extend(\"typescript\",{});delete n[\"class-name\"],e.languages.typescript[\"class-name\"].inside=n,e.languages.insertBefore(\"typescript\",\"function\",{decorator:{pattern:/@[$\\w\\xA0-\\uFFFF]+/,inside:{at:{pattern:/^@/,alias:\"operator\"},function:/^[\\s\\S]+/}},\"generic-function\":{pattern:/#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,greedy:!0,inside:{function:/^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,generic:{pattern:/<[\\s\\S]+/,alias:\"class-name\",inside:n}}}}),e.languages.ts=e.languages.typescript}(Prism),function(e){var n=e.languages.javascript,t=/\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})+\\}/.source,a=\"(@(?:arg|argument|param|property)\\\\s+(?:\"+t+\"\\\\s+)?)\";e.languages.jsdoc=e.languages.extend(\"javadoclike\",{parameter:{pattern:RegExp(a+/(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?=\\s|$)/.source),lookbehind:!0,inside:{punctuation:/\\./}}}),e.languages.insertBefore(\"jsdoc\",\"keyword\",{\"optional-parameter\":{pattern:RegExp(a+/\\[(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?:=[^[\\]]+)?\\](?=\\s|$)/.source),lookbehind:!0,inside:{parameter:{pattern:/(^\\[)[$\\w\\xA0-\\uFFFF\\.]+/,lookbehind:!0,inside:{punctuation:/\\./}},code:{pattern:/(=)[\\s\\S]*(?=\\]$)/,lookbehind:!0,inside:n,alias:\"language-javascript\"},punctuation:/[=[\\]]/}},\"class-name\":[{pattern:RegExp(/(@(?:augments|class|extends|interface|memberof!?|template|this|typedef)\\s+(?:<TYPE>\\s+)?)[A-Z]\\w*(?:\\.[A-Z]\\w*)*/.source.replace(/<TYPE>/g,function(){return t})),lookbehind:!0,inside:{punctuation:/\\./}},{pattern:RegExp(\"(@[a-z]+\\\\s+)\"+t),lookbehind:!0,inside:{string:n.string,number:n.number,boolean:n.boolean,keyword:e.languages.typescript.keyword,operator:/=>|\\.\\.\\.|[&|?:*]/,punctuation:/[.,;=<>{}()[\\]]/}}],example:{pattern:/(@example\\s+(?!\\s))(?:[^@\\s]|\\s+(?!\\s))+?(?=\\s*(?:\\*\\s*)?(?:@\\w|\\*\\/))/,lookbehind:!0,inside:{code:{pattern:/^([\\t ]*(?:\\*\\s*)?)\\S.*$/m,lookbehind:!0,inside:n,alias:\"language-javascript\"}}}}),e.languages.javadoclike.addSupport(\"javascript\",e.languages.jsdoc)}(Prism),function(e){e.languages.flow=e.languages.extend(\"javascript\",{}),e.languages.insertBefore(\"flow\",\"keyword\",{type:[{pattern:/\\b(?:[Bb]oolean|Function|[Nn]umber|[Ss]tring|[Ss]ymbol|any|mixed|null|void)\\b/,alias:\"class-name\"}]}),e.languages.flow[\"function-variable\"].pattern=/(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=\\s*(?:function\\b|(?:\\([^()]*\\)(?:\\s*:\\s*\\w+)?|(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/i,delete e.languages.flow.parameter,e.languages.insertBefore(\"flow\",\"operator\",{\"flow-punctuation\":{pattern:/\\{\\||\\|\\}/,alias:\"punctuation\"}}),Array.isArray(e.languages.flow.keyword)||(e.languages.flow.keyword=[e.languages.flow.keyword]),e.languages.flow.keyword.unshift({pattern:/(^|[^$]\\b)(?:Class|declare|opaque|type)\\b(?!\\$)/,lookbehind:!0},{pattern:/(^|[^$]\\B)\\$(?:Diff|Enum|Exact|Keys|ObjMap|PropertyType|Record|Shape|Subtype|Supertype|await)\\b(?!\\$)/,lookbehind:!0})}(Prism),Prism.languages.n4js=Prism.languages.extend(\"javascript\",{keyword:/\\b(?:Array|any|boolean|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|false|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|module|new|null|number|package|private|protected|public|return|set|static|string|super|switch|this|throw|true|try|typeof|var|void|while|with|yield)\\b/}),Prism.languages.insertBefore(\"n4js\",\"constant\",{annotation:{pattern:/@+\\w+/,alias:\"operator\"}}),Prism.languages.n4jsd=Prism.languages.n4js,function(e){function n(e,n){return RegExp(e.replace(/<ID>/g,function(){return/(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/.source}),n)}e.languages.insertBefore(\"javascript\",\"function-variable\",{\"method-variable\":{pattern:RegExp(\"(\\\\.\\\\s*)\"+e.languages.javascript[\"function-variable\"].pattern.source),lookbehind:!0,alias:[\"function-variable\",\"method\",\"function\",\"property-access\"]}}),e.languages.insertBefore(\"javascript\",\"function\",{method:{pattern:RegExp(\"(\\\\.\\\\s*)\"+e.languages.javascript.function.source),lookbehind:!0,alias:[\"function\",\"property-access\"]}}),e.languages.insertBefore(\"javascript\",\"constant\",{\"known-class-name\":[{pattern:/\\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\\b/,alias:\"class-name\"},{pattern:/\\b(?:[A-Z]\\w*)Error\\b/,alias:\"class-name\"}]}),e.languages.insertBefore(\"javascript\",\"keyword\",{imports:{pattern:n(/(\\bimport\\b\\s*)(?:<ID>(?:\\s*,\\s*(?:\\*\\s*as\\s+<ID>|\\{[^{}]*\\}))?|\\*\\s*as\\s+<ID>|\\{[^{}]*\\})(?=\\s*\\bfrom\\b)/.source),lookbehind:!0,inside:e.languages.javascript},exports:{pattern:n(/(\\bexport\\b\\s*)(?:\\*(?:\\s*as\\s+<ID>)?(?=\\s*\\bfrom\\b)|\\{[^{}]*\\})/.source),lookbehind:!0,inside:e.languages.javascript}}),e.languages.javascript.keyword.unshift({pattern:/\\b(?:as|default|export|from|import)\\b/,alias:\"module\"},{pattern:/\\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\\b/,alias:\"control-flow\"},{pattern:/\\bnull\\b/,alias:[\"null\",\"nil\"]},{pattern:/\\bundefined\\b/,alias:\"nil\"}),e.languages.insertBefore(\"javascript\",\"operator\",{spread:{pattern:/\\.{3}/,alias:\"operator\"},arrow:{pattern:/=>/,alias:\"operator\"}}),e.languages.insertBefore(\"javascript\",\"punctuation\",{\"property-access\":{pattern:n(/(\\.\\s*)#?<ID>/.source),lookbehind:!0},\"maybe-class-name\":{pattern:/(^|[^$\\w\\xA0-\\uFFFF])[A-Z][$\\w\\xA0-\\uFFFF]+/,lookbehind:!0},dom:{pattern:/\\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\\b/,alias:\"variable\"},console:{pattern:/\\bconsole(?=\\s*\\.)/,alias:\"class-name\"}});for(var t=[\"function\",\"function-variable\",\"method\",\"method-variable\",\"property-access\"],a=0;a<t.length;a++){var r=t[a],s=e.languages.javascript[r],r=(s=\"RegExp\"===e.util.type(s)?e.languages.javascript[r]={pattern:s}:s).inside||{};(s.inside=r)[\"maybe-class-name\"]=/^[A-Z][\\s\\S]*/}}(Prism),function(s){var e=s.util.clone(s.languages.javascript),t=/(?:\\s|\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))\\*\\/)/.source,a=/(?:\\{(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])*\\})/.source,r=/(?:\\{<S>*\\.{3}(?:[^{}]|<BRACES>)*\\})/.source;function n(e,n){return e=e.replace(/<S>/g,function(){return t}).replace(/<BRACES>/g,function(){return a}).replace(/<SPREAD>/g,function(){return r}),RegExp(e,n)}r=n(r).source,s.languages.jsx=s.languages.extend(\"markup\",e),s.languages.jsx.tag.pattern=n(/<\\/?(?:[\\w.:-]+(?:<S>+(?:[\\w.:$-]+(?:=(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s{'\"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\\/?)?>/.source),s.languages.jsx.tag.inside.tag.pattern=/^<\\/?[^\\s>\\/]*/,s.languages.jsx.tag.inside[\"attr-value\"].pattern=/=(?!\\{)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s'\">]+)/,s.languages.jsx.tag.inside.tag.inside[\"class-name\"]=/^[A-Z]\\w*(?:\\.[A-Z]\\w*)*$/,s.languages.jsx.tag.inside.comment=e.comment,s.languages.insertBefore(\"inside\",\"attr-name\",{spread:{pattern:n(/<SPREAD>/.source),inside:s.languages.jsx}},s.languages.jsx.tag),s.languages.insertBefore(\"inside\",\"special-attr\",{script:{pattern:n(/=<BRACES>/.source),alias:\"language-javascript\",inside:{\"script-punctuation\":{pattern:/^=(?=\\{)/,alias:\"punctuation\"},rest:s.languages.jsx}}},s.languages.jsx.tag);function i(e){for(var n=[],t=0;t<e.length;t++){var a=e[t],r=!1;\"string\"!=typeof a&&(\"tag\"===a.type&&a.content[0]&&\"tag\"===a.content[0].type?\"</\"===a.content[0].content[0].content?0<n.length&&n[n.length-1].tagName===o(a.content[0].content[1])&&n.pop():\"/>\"!==a.content[a.content.length-1].content&&n.push({tagName:o(a.content[0].content[1]),openedBraces:0}):0<n.length&&\"punctuation\"===a.type&&\"{\"===a.content?n[n.length-1].openedBraces++:0<n.length&&0<n[n.length-1].openedBraces&&\"punctuation\"===a.type&&\"}\"===a.content?n[n.length-1].openedBraces--:r=!0),(r||\"string\"==typeof a)&&0<n.length&&0===n[n.length-1].openedBraces&&(r=o(a),t<e.length-1&&(\"string\"==typeof e[t+1]||\"plain-text\"===e[t+1].type)&&(r+=o(e[t+1]),e.splice(t+1,1)),0<t&&(\"string\"==typeof e[t-1]||\"plain-text\"===e[t-1].type)&&(r=o(e[t-1])+r,e.splice(t-1,1),t--),e[t]=new s.Token(\"plain-text\",r,null,r)),a.content&&\"string\"!=typeof a.content&&i(a.content)}}var o=function(e){return e?\"string\"==typeof e?e:\"string\"==typeof e.content?e.content:e.content.map(o).join(\"\"):\"\"};s.hooks.add(\"after-tokenize\",function(e){\"jsx\"!==e.language&&\"tsx\"!==e.language||i(e.tokens)})}(Prism),function(e){var n=e.util.clone(e.languages.typescript),n=(e.languages.tsx=e.languages.extend(\"jsx\",n),delete e.languages.tsx.parameter,delete e.languages.tsx[\"literal-property\"],e.languages.tsx.tag);n.pattern=RegExp(/(^|[^\\w$]|(?=<\\/))/.source+\"(?:\"+n.pattern.source+\")\",n.pattern.flags),n.lookbehind=!0}(Prism),Prism.languages.swift={comment:{pattern:/(^|[^\\\\:])(?:\\/\\/.*|\\/\\*(?:[^/*]|\\/(?!\\*)|\\*(?!\\/)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\*\\/)/,lookbehind:!0,greedy:!0},\"string-literal\":[{pattern:RegExp(/(^|[^\"#])/.source+\"(?:\"+/\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^(])|[^\\\\\\r\\n\"])*\"/.source+\"|\"+/\"\"\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|[^(])|[^\\\\\"]|\"(?!\"\"))*\"\"\"/.source+\")\"+/(?![\"#])/.source),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\\\\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,lookbehind:!0,inside:null},\"interpolation-punctuation\":{pattern:/^\\)|\\\\\\($/,alias:\"punctuation\"},punctuation:/\\\\(?=[\\r\\n])/,string:/[\\s\\S]+/}},{pattern:RegExp(/(^|[^\"#])(#+)/.source+\"(?:\"+/\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^#])|[^\\\\\\r\\n])*?\"/.source+\"|\"+/\"\"\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|[^#])|[^\\\\])*?\"\"\"/.source+\")\\\\2\"),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\\\#+\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,lookbehind:!0,inside:null},\"interpolation-punctuation\":{pattern:/^\\)|\\\\#+\\($/,alias:\"punctuation\"},string:/[\\s\\S]+/}}],directive:{pattern:RegExp(/#/.source+\"(?:\"+/(?:elseif|if)\\b/.source+\"(?:[ \\t]*\"+/(?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*\\((?:[^()]|\\([^()]*\\))*\\))?|\\((?:[^()]|\\([^()]*\\))*\\))(?:[ \\t]*(?:&&|\\|\\|))?/.source+\")+|\"+/(?:else|endif)\\b/.source+\")\"),alias:\"property\",inside:{\"directive-name\":/^#\\w+/,boolean:/\\b(?:false|true)\\b/,number:/\\b\\d+(?:\\.\\d+)*\\b/,operator:/!|&&|\\|\\||[<>]=?/,punctuation:/[(),]/}},literal:{pattern:/#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\\b/,alias:\"constant\"},\"other-directive\":{pattern:/#\\w+\\b/,alias:\"property\"},attribute:{pattern:/@\\w+/,alias:\"atrule\"},\"function-definition\":{pattern:/(\\bfunc\\s+)\\w+/,lookbehind:!0,alias:\"function\"},label:{pattern:/\\b(break|continue)\\s+\\w+|\\b[a-zA-Z_]\\w*(?=\\s*:\\s*(?:for|repeat|while)\\b)/,lookbehind:!0,alias:\"important\"},keyword:/\\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\\b/,boolean:/\\b(?:false|true)\\b/,nil:{pattern:/\\bnil\\b/,alias:\"constant\"},\"short-argument\":/\\$\\d+\\b/,omit:{pattern:/\\b_\\b/,alias:\"keyword\"},number:/\\b(?:[\\d_]+(?:\\.[\\de_]+)?|0x[a-f0-9_]+(?:\\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\\b/i,\"class-name\":/\\b[A-Z](?:[A-Z_\\d]*[a-z]\\w*)?\\b/,function:/\\b[a-z_]\\w*(?=\\s*\\()/i,constant:/\\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\\b/,operator:/[-+*/%=!<>&|^~?]+|\\.[.\\-+*/%=!<>&|^~?]+/,punctuation:/[{}[\\]();,.:\\\\]/},Prism.languages.swift[\"string-literal\"].forEach(function(e){e.inside.interpolation.inside=Prism.languages.swift}),function(e){e.languages.kotlin=e.languages.extend(\"clike\",{keyword:{pattern:/(^|[^.])\\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\\b/,lookbehind:!0},function:[{pattern:/(?:`[^\\r\\n`]+`|\\b\\w+)(?=\\s*\\()/,greedy:!0},{pattern:/(\\.)(?:`[^\\r\\n`]+`|\\w+)(?=\\s*\\{)/,lookbehind:!0,greedy:!0}],number:/\\b(?:0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?[fFL]?)\\b/,operator:/\\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\\/*%<>]=?|[?:]:?|\\.\\.|&&|\\|\\||\\b(?:and|inv|or|shl|shr|ushr|xor)\\b/}),delete e.languages.kotlin[\"class-name\"];var n={\"interpolation-punctuation\":{pattern:/^\\$\\{?|\\}$/,alias:\"punctuation\"},expression:{pattern:/[\\s\\S]+/,inside:e.languages.kotlin}};e.languages.insertBefore(\"kotlin\",\"string\",{\"string-literal\":[{pattern:/\"\"\"(?:[^$]|\\$(?:(?!\\{)|\\{[^{}]*\\}))*?\"\"\"/,alias:\"multiline\",inside:{interpolation:{pattern:/\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,inside:n},string:/[\\s\\S]+/}},{pattern:/\"(?:[^\"\\\\\\r\\n$]|\\\\.|\\$(?:(?!\\{)|\\{[^{}]*\\}))*\"/,alias:\"singleline\",inside:{interpolation:{pattern:/((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,lookbehind:!0,inside:n},string:/[\\s\\S]+/}}],char:{pattern:/'(?:[^'\\\\\\r\\n]|\\\\(?:.|u[a-fA-F0-9]{0,4}))'/,greedy:!0}}),delete e.languages.kotlin.string,e.languages.insertBefore(\"kotlin\",\"keyword\",{annotation:{pattern:/\\B@(?:\\w+:)?(?:[A-Z]\\w*|\\[[^\\]]+\\])/,alias:\"builtin\"}}),e.languages.insertBefore(\"kotlin\",\"function\",{label:{pattern:/\\b\\w+@|@\\w+\\b/,alias:\"symbol\"}}),e.languages.kt=e.languages.kotlin,e.languages.kts=e.languages.kotlin}(Prism),Prism.languages.c=Prism.languages.extend(\"clike\",{comment:{pattern:/\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,greedy:!0},string:{pattern:/\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,greedy:!0},\"class-name\":{pattern:/(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,lookbehind:!0},keyword:/\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,function:/\\b[a-z_]\\w*(?=\\s*\\()/i,number:/(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,operator:/>>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/}),Prism.languages.insertBefore(\"c\",\"string\",{char:{pattern:/'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,greedy:!0}}),Prism.languages.insertBefore(\"c\",\"string\",{macro:{pattern:/(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,lookbehind:!0,greedy:!0,alias:\"property\",inside:{string:[{pattern:/^(#\\s*include\\s*)<[^>]+>/,lookbehind:!0},Prism.languages.c.string],char:Prism.languages.c.char,comment:Prism.languages.c.comment,\"macro-name\":[{pattern:/(^#\\s*define\\s+)\\w+\\b(?!\\()/i,lookbehind:!0},{pattern:/(^#\\s*define\\s+)\\w+\\b(?=\\()/i,lookbehind:!0,alias:\"function\"}],directive:{pattern:/^(#\\s*)[a-z]+/,lookbehind:!0,alias:\"keyword\"},\"directive-hash\":/^#/,punctuation:/##|\\\\(?=[\\r\\n])/,expression:{pattern:/\\S[\\s\\S]*/,inside:Prism.languages.c}}}}),Prism.languages.insertBefore(\"c\",\"function\",{constant:/\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/}),delete Prism.languages.c.boolean,Prism.languages.objectivec=Prism.languages.extend(\"c\",{string:{pattern:/@?\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,greedy:!0},keyword:/\\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\\b/,operator:/-[->]?|\\+\\+?|!=?|<<?=?|>>?=?|==?|&&?|\\|\\|?|[~^%?*\\/@]/}),delete Prism.languages.objectivec[\"class-name\"],Prism.languages.objc=Prism.languages.objectivec,Prism.languages.reason=Prism.languages.extend(\"clike\",{string:{pattern:/\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n\"])*\"/,greedy:!0},\"class-name\":/\\b[A-Z]\\w*/,keyword:/\\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\\b/,operator:/\\.{3}|:[:=]|\\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\\-*\\/]\\.?|\\b(?:asr|land|lor|lsl|lsr|lxor|mod)\\b/}),Prism.languages.insertBefore(\"reason\",\"class-name\",{char:{pattern:/'(?:\\\\x[\\da-f]{2}|\\\\o[0-3][0-7][0-7]|\\\\\\d{3}|\\\\.|[^'\\\\\\r\\n])'/,greedy:!0},constructor:/\\b[A-Z]\\w*\\b(?!\\s*\\.)/,label:{pattern:/\\b[a-z]\\w*(?=::)/,alias:\"symbol\"}}),delete Prism.languages.reason.function,function(e){for(var n=/\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\//.source,t=0;t<2;t++)n=n.replace(/<self>/g,function(){return n});n=n.replace(/<self>/g,function(){return/[^\\s\\S]/.source}),e.languages.rust={comment:[{pattern:RegExp(/(^|[^\\\\])/.source+n),lookbehind:!0,greedy:!0},{pattern:/(^|[^\\\\:])\\/\\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/b?\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|b?r(#*)\"(?:[^\"]|\"(?!\\1))*\"\\1/,greedy:!0},char:{pattern:/b?'(?:\\\\(?:x[0-7][\\da-fA-F]|u\\{(?:[\\da-fA-F]_*){1,6}\\}|.)|[^\\\\\\r\\n\\t'])'/,greedy:!0},attribute:{pattern:/#!?\\[(?:[^\\[\\]\"]|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")*\\]/,greedy:!0,alias:\"attr-name\",inside:{string:null}},\"closure-params\":{pattern:/([=(,:]\\s*|\\bmove\\s*)\\|[^|]*\\||\\|[^|]*\\|(?=\\s*(?:\\{|->))/,lookbehind:!0,greedy:!0,inside:{\"closure-punctuation\":{pattern:/^\\||\\|$/,alias:\"punctuation\"},rest:null}},\"lifetime-annotation\":{pattern:/'\\w+/,alias:\"symbol\"},\"fragment-specifier\":{pattern:/(\\$\\w+:)[a-z]+/,lookbehind:!0,alias:\"punctuation\"},variable:/\\$\\w+/,\"function-definition\":{pattern:/(\\bfn\\s+)\\w+/,lookbehind:!0,alias:\"function\"},\"type-definition\":{pattern:/(\\b(?:enum|struct|trait|type|union)\\s+)\\w+/,lookbehind:!0,alias:\"class-name\"},\"module-declaration\":[{pattern:/(\\b(?:crate|mod)\\s+)[a-z][a-z_\\d]*/,lookbehind:!0,alias:\"namespace\"},{pattern:/(\\b(?:crate|self|super)\\s*)::\\s*[a-z][a-z_\\d]*\\b(?:\\s*::(?:\\s*[a-z][a-z_\\d]*\\s*::)*)?/,lookbehind:!0,alias:\"namespace\",inside:{punctuation:/::/}}],keyword:[/\\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\\b/,/\\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\\b/],function:/\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())/,macro:{pattern:/\\b\\w+!/,alias:\"property\"},constant:/\\b[A-Z_][A-Z_\\d]+\\b/,\"class-name\":/\\b[A-Z]\\w*\\b/,namespace:{pattern:/(?:\\b[a-z][a-z_\\d]*\\s*::\\s*)*\\b[a-z][a-z_\\d]*\\s*::(?!\\s*<)/,inside:{punctuation:/::/}},number:/\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\\b/,boolean:/\\b(?:false|true)\\b/,punctuation:/->|\\.\\.=|\\.{1,3}|::|[{}[\\];(),:]/,operator:/[-+*\\/%!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?=?|[@?]/},e.languages.rust[\"closure-params\"].inside.rest=e.languages.rust,e.languages.rust.attribute.inside.string=e.languages.rust.string}(Prism),Prism.languages.go=Prism.languages.extend(\"clike\",{string:{pattern:/(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`[^`]*`/,lookbehind:!0,greedy:!0},keyword:/\\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\\b/,boolean:/\\b(?:_|false|iota|nil|true)\\b/,number:[/\\b0(?:b[01_]+|o[0-7_]+)i?\\b/i,/\\b0x(?:[a-f\\d_]+(?:\\.[a-f\\d_]*)?|\\.[a-f\\d_]+)(?:p[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i,/(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?[\\d_]+)?i?(?!\\w)/i],operator:/[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\./,builtin:/\\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\\b/}),Prism.languages.insertBefore(\"go\",\"string\",{char:{pattern:/'(?:\\\\.|[^'\\\\\\r\\n]){0,10}'/,greedy:!0}}),delete Prism.languages.go[\"class-name\"],function(e){var n=/\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/,t=/\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(/<keyword>/g,function(){return n.source});e.languages.cpp=e.languages.extend(\"c\",{\"class-name\":[{pattern:RegExp(/(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source.replace(/<keyword>/g,function(){return n.source})),lookbehind:!0},/\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/,/\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i,/\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/],keyword:n,number:{pattern:/(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i,greedy:!0},operator:/>>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/,boolean:/\\b(?:false|true)\\b/}),e.languages.insertBefore(\"cpp\",\"string\",{module:{pattern:RegExp(/(\\b(?:import|module)\\s+)/.source+\"(?:\"+/\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source+\"|\"+/<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(/<mod-name>/g,function(){return t})+\")\"),lookbehind:!0,greedy:!0,inside:{string:/^[<\"][\\s\\S]+/,operator:/:/,punctuation:/\\./}},\"raw-string\":{pattern:/R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/,alias:\"string\",greedy:!0}}),e.languages.insertBefore(\"cpp\",\"keyword\",{\"generic-function\":{pattern:/\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i,inside:{function:/^\\w+/,generic:{pattern:/<[\\s\\S]+/,alias:\"class-name\",inside:e.languages.cpp}}}}),e.languages.insertBefore(\"cpp\",\"operator\",{\"double-colon\":{pattern:/::/,alias:\"punctuation\"}}),e.languages.insertBefore(\"cpp\",\"class-name\",{\"base-clause\":{pattern:/(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/,lookbehind:!0,greedy:!0,inside:e.languages.extend(\"cpp\",{})}}),e.languages.insertBefore(\"inside\",\"double-colon\",{\"class-name\":/\\b[a-z_]\\w*\\b(?!\\s*::)/i},e.languages.cpp[\"base-clause\"])}(Prism),Prism.languages.python={comment:{pattern:/(^|[^\\\\])#.*/,lookbehind:!0,greedy:!0},\"string-interpolation\":{pattern:/(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,lookbehind:!0,inside:{\"format-spec\":{pattern:/(:)[^:(){}]+(?=\\}$)/,lookbehind:!0},\"conversion-option\":{pattern:/![sra](?=[:}]$)/,alias:\"punctuation\"},rest:null}},string:/[\\s\\S]+/}},\"triple-quoted-string\":{pattern:/(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,greedy:!0,alias:\"string\"},string:{pattern:/(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,greedy:!0},function:{pattern:/((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,lookbehind:!0},\"class-name\":{pattern:/(\\bclass\\s+)\\w+/i,lookbehind:!0},decorator:{pattern:/(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,lookbehind:!0,alias:[\"annotation\",\"punctuation\"],inside:{punctuation:/\\./}},keyword:/\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,builtin:/\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,boolean:/\\b(?:False|None|True)\\b/,number:/\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,operator:/[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\\];(),.:]/},Prism.languages.python[\"string-interpolation\"].inside.interpolation.inside.rest=Prism.languages.python,Prism.languages.py=Prism.languages.python,Prism.languages.json={property:{pattern:/(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?!\\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,greedy:!0},number:/-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,punctuation:/[{}[\\],]/,operator:/:/,boolean:/\\b(?:false|true)\\b/,null:{pattern:/\\bnull\\b/,alias:\"keyword\"}},Prism.languages.webmanifest=Prism.languages.json;", "export { default as dracula } from \"./dracula\"\nexport { default as duotoneDark } from \"./duotoneDark\"\nexport { default as duotoneLight } from \"./duotoneLight\"\nexport { default as github } from \"./github\"\nexport { default as nightOwl } from \"./nightOwl\"\nexport { default as nightOwlLight } from \"./nightOwlLight\"\nexport { default as oceanicNext } from \"./oceanicNext\"\nexport { default as okaidia } from \"./okaidia\"\nexport { default as palenight } from \"./palenight\"\nexport { default as shadesOfPurple } from \"./shadesOfPurple\"\nexport { default as synthwave84 } from \"./synthwave84\"\nexport { default as ultramin } from \"./ultramin\"\nexport { default as vsDark } from \"./vsDark\"\nexport { default as vsLight } from \"./vsLight\"\nexport { default as jettwaveDark } from \"./jettwaveDark\"\nexport { default as jettwaveLight } from \"./jettwaveLight\"\nexport { default as oneDark } from \"./oneDark\"\nexport { default as oneLight } from \"./oneLight\"\nexport { default as gruvboxMaterialDark } from \"./gruvboxMaterialDark\"\nexport { default as gruvboxMaterialLight } from \"./gruvboxMaterialLight\"\n", "// Original: https://github.com/dracula/visual-studio-code\n// Converted automatically using ./tools/themeFromVsCode\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#F8F8F2\",\n    backgroundColor: \"#282A36\",\n  },\n  styles: [\n    {\n      types: [\"prolog\", \"constant\", \"builtin\"],\n      style: {\n        color: \"rgb(189, 147, 249)\",\n      },\n    },\n    {\n      types: [\"inserted\", \"function\"],\n      style: {\n        color: \"rgb(80, 250, 123)\",\n      },\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"rgb(255, 85, 85)\",\n      },\n    },\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(255, 184, 108)\",\n      },\n    },\n    {\n      types: [\"punctuation\", \"symbol\"],\n      style: {\n        color: \"rgb(248, 248, 242)\",\n      },\n    },\n    {\n      types: [\"string\", \"char\", \"tag\", \"selector\"],\n      style: {\n        color: \"rgb(255, 121, 198)\",\n      },\n    },\n    {\n      types: [\"keyword\", \"variable\"],\n      style: {\n        color: \"rgb(189, 147, 249)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(98, 114, 164)\",\n      },\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"rgb(241, 250, 140)\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Duotone Dark\n// Author: <PERSON><PERSON><PERSON>, adapted from DuoTone themes for Atom (http://simurai.com/projects/2016/01/01/duotone-themes)\n// Conversion: <PERSON> (http://atelierbram.github.io/Base2Tone-prism/output/prism/prism-base2tone-evening-dark.css)\n// Generated with Base16 Builder (https://github.com/base16-builder/base16-builder)\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    backgroundColor: \"#2a2734\",\n    color: \"#9a86fd\",\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"doctype\", \"cdata\", \"punctuation\"],\n      style: {\n        color: \"#6c6783\",\n      },\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        opacity: 0.7,\n      },\n    },\n    {\n      types: [\"tag\", \"operator\", \"number\"],\n      style: {\n        color: \"#e09142\",\n      },\n    },\n    {\n      types: [\"property\", \"function\"],\n      style: {\n        color: \"#9a86fd\",\n      },\n    },\n    {\n      types: [\"tag-id\", \"selector\", \"atrule-id\"],\n      style: {\n        color: \"#eeebff\",\n      },\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"#c4b9fe\",\n      },\n    },\n    {\n      types: [\n        \"boolean\",\n        \"string\",\n        \"entity\",\n        \"url\",\n        \"attr-value\",\n        \"keyword\",\n        \"control\",\n        \"directive\",\n        \"unit\",\n        \"statement\",\n        \"regex\",\n        \"atrule\",\n        \"placeholder\",\n        \"variable\",\n      ],\n      style: {\n        color: \"#ffcc99\",\n      },\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        textDecorationLine: \"line-through\",\n      },\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        textDecorationLine: \"underline\",\n      },\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\",\n      },\n    },\n    {\n      types: [\"important\"],\n      style: {\n        color: \"#c4b9fe\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Duotone Light\n// Author: <PERSON><PERSON><PERSON>, adapted from DuoTone themes for Atom (http://simurai.com/projects/2016/01/01/duotone-themes)\n// Conversion: <PERSON> (http://atelierbram.github.io/Base2Tone-prism/output/prism/prism-base2tone-evening-dark.css)\n// Generated with Base16 Builder (https://github.com/base16-builder/base16-builder)\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    backgroundColor: \"#faf8f5\",\n    color: \"#728fcb\",\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"doctype\", \"cdata\", \"punctuation\"],\n      style: {\n        color: \"#b6ad9a\",\n      },\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        opacity: 0.7,\n      },\n    },\n    {\n      types: [\"tag\", \"operator\", \"number\"],\n      style: {\n        color: \"#063289\",\n      },\n    },\n    {\n      types: [\"property\", \"function\"],\n      style: {\n        color: \"#b29762\",\n      },\n    },\n    {\n      types: [\"tag-id\", \"selector\", \"atrule-id\"],\n      style: {\n        color: \"#2d2006\",\n      },\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"#896724\",\n      },\n    },\n    {\n      types: [\n        \"boolean\",\n        \"string\",\n        \"entity\",\n        \"url\",\n        \"attr-value\",\n        \"keyword\",\n        \"control\",\n        \"directive\",\n        \"unit\",\n        \"statement\",\n        \"regex\",\n        \"atrule\",\n      ],\n      style: {\n        color: \"#728fcb\",\n      },\n    },\n    {\n      types: [\"placeholder\", \"variable\"],\n      style: {\n        color: \"#93abdc\",\n      },\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        textDecorationLine: \"line-through\",\n      },\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        textDecorationLine: \"underline\",\n      },\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\",\n      },\n    },\n    {\n      types: [\"important\"],\n      style: {\n        color: \"#896724\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Original: https://raw.githubusercontent.com/PrismJS/prism-themes/master/themes/prism-ghcolors.css\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#393A34\",\n    backgroundColor: \"#f6f8fa\",\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"doctype\", \"cdata\"],\n      style: {\n        color: \"#999988\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        opacity: 0.7,\n      },\n    },\n    {\n      types: [\"string\", \"attr-value\"],\n      style: {\n        color: \"#e3116c\",\n      },\n    },\n    {\n      types: [\"punctuation\", \"operator\"],\n      style: {\n        color: \"#393A34\",\n      },\n    },\n    {\n      types: [\n        \"entity\",\n        \"url\",\n        \"symbol\",\n        \"number\",\n        \"boolean\",\n        \"variable\",\n        \"constant\",\n        \"property\",\n        \"regex\",\n        \"inserted\",\n      ],\n      style: {\n        color: \"#36acaa\",\n      },\n    },\n    {\n      types: [\"atrule\", \"keyword\", \"attr-name\", \"selector\"],\n      style: {\n        color: \"#00a4db\",\n      },\n    },\n    {\n      types: [\"function\", \"deleted\", \"tag\"],\n      style: {\n        color: \"#d73a49\",\n      },\n    },\n    {\n      types: [\"function-variable\"],\n      style: {\n        color: \"#6f42c1\",\n      },\n    },\n    {\n      types: [\"tag\", \"selector\", \"keyword\"],\n      style: {\n        color: \"#00009f\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Original: https://github.com/sdras/night-owl-vscode-theme\n// Converted automatically using ./tools/themeFromVsCode\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#d6deeb\",\n    backgroundColor: \"#011627\",\n  },\n  styles: [\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(162, 191, 252)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"rgba(239, 83, 80, 0.56)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"inserted\", \"attr-name\"],\n      style: {\n        color: \"rgb(173, 219, 103)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(99, 119, 119)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"string\", \"url\"],\n      style: {\n        color: \"rgb(173, 219, 103)\",\n      },\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"rgb(214, 222, 235)\",\n      },\n    },\n    {\n      types: [\"number\"],\n      style: {\n        color: \"rgb(247, 140, 108)\",\n      },\n    },\n    {\n      types: [\"builtin\", \"char\", \"constant\", \"function\"],\n      style: {\n        color: \"rgb(130, 170, 255)\",\n      },\n    },\n    {\n      // This was manually added after the auto-generation\n      // so that punctuations are not italicised\n      types: [\"punctuation\"],\n      style: {\n        color: \"rgb(199, 146, 234)\",\n      },\n    },\n    {\n      types: [\"selector\", \"doctype\"],\n      style: {\n        color: \"rgb(199, 146, 234)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"rgb(255, 203, 139)\",\n      },\n    },\n    {\n      types: [\"tag\", \"operator\", \"keyword\"],\n      style: {\n        color: \"rgb(127, 219, 202)\",\n      },\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: \"rgb(255, 88, 116)\",\n      },\n    },\n    {\n      types: [\"property\"],\n      style: {\n        color: \"rgb(128, 203, 196)\",\n      },\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        color: \"rgb(178, 204, 214)\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Original: https://github.com/sdras/night-owl-vscode-theme\n// Converted automatically using ./tools/themeFromVsCode\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#403f53\",\n    backgroundColor: \"#FBFBFB\",\n  },\n  styles: [\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(162, 191, 252)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"rgba(239, 83, 80, 0.56)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"inserted\", \"attr-name\"],\n      style: {\n        color: \"rgb(72, 118, 214)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(152, 159, 177)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"string\", \"builtin\", \"char\", \"constant\", \"url\"],\n      style: {\n        color: \"rgb(72, 118, 214)\",\n      },\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"rgb(201, 103, 101)\",\n      },\n    },\n    {\n      types: [\"number\"],\n      style: {\n        color: \"rgb(170, 9, 130)\",\n      },\n    },\n    {\n      // This was manually added after the auto-generation\n      // so that punctuations are not italicised\n      types: [\"punctuation\"],\n      style: {\n        color: \"rgb(153, 76, 195)\",\n      },\n    },\n    {\n      types: [\"function\", \"selector\", \"doctype\"],\n      style: {\n        color: \"rgb(153, 76, 195)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"rgb(17, 17, 17)\",\n      },\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"rgb(153, 76, 195)\",\n      },\n    },\n    {\n      types: [\"operator\", \"property\", \"keyword\", \"namespace\"],\n      style: {\n        color: \"rgb(12, 150, 155)\",\n      },\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: \"rgb(188, 84, 84)\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Oceanic Next\n// Author: <PERSON><PERSON><PERSON> (https://github.com/v<PERSON>)\n// https://github.com/voronianski/oceanic-next-color-scheme\n// Adapted from: https://github.com/reactjs/reactjs.org/blob/428d52b/src/prism-styles.js\nimport type { PrismTheme } from \"../types\"\nconst colors = {\n  char: \"#D8DEE9\",\n  comment: \"#999999\",\n  keyword: \"#c5a5c5\",\n  primitive: \"#5a9bcf\",\n  string: \"#8dc891\",\n  variable: \"#d7deea\",\n  boolean: \"#ff8b50\",\n  punctuation: \"#5FB3B3\",\n  tag: \"#fc929e\",\n  function: \"#79b6f2\",\n  className: \"#FAC863\",\n  method: \"#6699CC\",\n  operator: \"#fc929e\",\n}\nconst theme: PrismTheme = {\n  plain: {\n    backgroundColor: \"#282c34\",\n    color: \"#ffffff\",\n  },\n  styles: [\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: colors.keyword,\n      },\n    },\n    {\n      types: [\"attr-value\"],\n      style: {\n        color: colors.string,\n      },\n    },\n    {\n      types: [\n        \"comment\",\n        \"block-comment\",\n        \"prolog\",\n        \"doctype\",\n        \"cdata\",\n        \"shebang\",\n      ],\n      style: {\n        color: colors.comment,\n      },\n    },\n    {\n      types: [\n        \"property\",\n        \"number\",\n        \"function-name\",\n        \"constant\",\n        \"symbol\",\n        \"deleted\",\n      ],\n      style: {\n        color: colors.primitive,\n      },\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: colors.boolean,\n      },\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: colors.tag,\n      },\n    },\n    {\n      types: [\"string\"],\n      style: {\n        color: colors.string,\n      },\n    },\n    {\n      types: [\"punctuation\"],\n      style: {\n        color: colors.string,\n      },\n    },\n    {\n      types: [\"selector\", \"char\", \"builtin\", \"inserted\"],\n      style: {\n        color: colors.char,\n      },\n    },\n    {\n      types: [\"function\"],\n      style: {\n        color: colors.function,\n      },\n    },\n    {\n      types: [\"operator\", \"entity\", \"url\", \"variable\"],\n      style: {\n        color: colors.variable,\n      },\n    },\n    {\n      types: [\"keyword\"],\n      style: {\n        color: colors.keyword,\n      },\n    },\n    {\n      types: [\"atrule\", \"class-name\"],\n      style: {\n        color: colors.className,\n      },\n    },\n    {\n      types: [\"important\"],\n      style: {\n        fontWeight: \"400\",\n      },\n    },\n    {\n      types: [\"bold\"],\n      style: {\n        fontWeight: \"bold\",\n      },\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        opacity: 0.7,\n      },\n    },\n  ],\n}\nexport default theme\n", "/*\n    Adapted from the Prism Okaidia theme\n    https://github.com/PrismJS/prism/blob/1761513e3db48ca9222037644a9c68746e24f039/themes/prism-okaidia.css\n    https://github.com/ocodia/okaidia-prismjs-theme\n*/\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#f8f8f2\",\n    backgroundColor: \"#272822\",\n  },\n  styles: [\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(162, 191, 252)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"#f92672\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        color: \"rgb(173, 219, 103)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"#8292a2\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"string\", \"url\"],\n      style: {\n        color: \"#a6e22e\",\n      },\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"#f8f8f2\",\n      },\n    },\n    {\n      types: [\"number\"],\n      style: {\n        color: \"#ae81ff\",\n      },\n    },\n    {\n      types: [\"builtin\", \"char\", \"constant\", \"function\", \"class-name\"],\n      style: {\n        color: \"#e6db74\",\n      },\n    },\n    {\n      types: [\"punctuation\"],\n      style: {\n        color: \"#f8f8f2\",\n      },\n    },\n    {\n      types: [\"selector\", \"doctype\"],\n      style: {\n        color: \"#a6e22e\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"tag\", \"operator\", \"keyword\"],\n      style: {\n        color: \"#66d9ef\",\n      },\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: \"#ae81ff\",\n      },\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        color: \"rgb(178, 204, 214)\",\n        opacity: 0.7,\n      },\n    },\n    {\n      types: [\"tag\", \"property\"],\n      style: {\n        color: \"#f92672\",\n      },\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"#a6e22e !important\",\n      },\n    },\n    {\n      types: [\"doctype\"],\n      style: {\n        color: \"#8292a2\",\n      },\n    },\n    {\n      types: [\"rule\"],\n      style: {\n        color: \"#e6db74\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Converted automatically using ./tools/themeFromVsCode\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#bfc7d5\",\n    backgroundColor: \"#292d3e\",\n  },\n  styles: [\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(105, 112, 152)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"string\", \"inserted\"],\n      style: {\n        color: \"rgb(195, 232, 141)\",\n      },\n    },\n    {\n      types: [\"number\"],\n      style: {\n        color: \"rgb(247, 140, 108)\",\n      },\n    },\n    {\n      types: [\"builtin\", \"char\", \"constant\", \"function\"],\n      style: {\n        color: \"rgb(130, 170, 255)\",\n      },\n    },\n    {\n      types: [\"punctuation\", \"selector\"],\n      style: {\n        color: \"rgb(199, 146, 234)\",\n      },\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"rgb(191, 199, 213)\",\n      },\n    },\n    {\n      types: [\"class-name\", \"attr-name\"],\n      style: {\n        color: \"rgb(255, 203, 107)\",\n      },\n    },\n    {\n      types: [\"tag\", \"deleted\"],\n      style: {\n        color: \"rgb(255, 85, 114)\",\n      },\n    },\n    {\n      types: [\"operator\"],\n      style: {\n        color: \"rgb(137, 221, 255)\",\n      },\n    },\n    {\n      types: [\"boolean\"],\n      style: {\n        color: \"rgb(255, 88, 116)\",\n      },\n    },\n    {\n      types: [\"keyword\"],\n      style: {\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"doctype\"],\n      style: {\n        color: \"rgb(199, 146, 234)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"namespace\"],\n      style: {\n        color: \"rgb(178, 204, 214)\",\n      },\n    },\n    {\n      types: [\"url\"],\n      style: {\n        color: \"rgb(221, 221, 221)\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Shades of Purple\n// Author: <PERSON> https://twitter.com/MrAhmadAwais\n// Original: https://github.com/ahmadawais/shades-of-purple-vscode/\n// Converted automatically using ./tools/themeFromVsCode and then customized manually.\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#9EFEFF\",\n    backgroundColor: \"#2D2A55\",\n  },\n  styles: [\n    {\n      types: [\"changed\"],\n      style: {\n        color: \"rgb(255, 238, 128)\",\n      },\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        color: \"rgba(239, 83, 80, 0.56)\",\n      },\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        color: \"rgb(173, 219, 103)\",\n      },\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(179, 98, 255)\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"punctuation\"],\n      style: {\n        color: \"rgb(255, 255, 255)\",\n      },\n    },\n    {\n      types: [\"constant\"],\n      style: {\n        color: \"rgb(255, 98, 140)\",\n      },\n    },\n    {\n      types: [\"string\", \"url\"],\n      style: {\n        color: \"rgb(165, 255, 144)\",\n      },\n    },\n    {\n      types: [\"variable\"],\n      style: {\n        color: \"rgb(255, 238, 128)\",\n      },\n    },\n    {\n      types: [\"number\", \"boolean\"],\n      style: {\n        color: \"rgb(255, 98, 140)\",\n      },\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"rgb(255, 180, 84)\",\n      },\n    },\n    {\n      types: [\n        \"keyword\",\n        \"operator\",\n        \"property\",\n        \"namespace\",\n        \"tag\",\n        \"selector\",\n        \"doctype\",\n      ],\n      style: {\n        color: \"rgb(255, 157, 0)\",\n      },\n    },\n    {\n      types: [\"builtin\", \"char\", \"constant\", \"function\", \"class-name\"],\n      style: {\n        color: \"rgb(250, 208, 0)\",\n      },\n    },\n  ],\n}\nexport default theme\n", "/*\n * Synthwave '84 Theme originally by <PERSON><PERSON> [@Robb0wen] for Visual Studio Code\n * Demo: https://marc.dev/demo/prism-synthwave84\n *\n * Ported for PrismJS by <PERSON> [@themarcba]: https://github.com/themarcba/prism-themes/blob/master/themes/prism-synthwave84.css\n * Ported for prism-react-renderer by <PERSON> [@forrest-akin]\n */\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    backgroundColor: \"linear-gradient(to bottom, #2a2139 75%, #34294f)\",\n    backgroundImage: \"#34294f\",\n    color: \"#f92aad\",\n    textShadow: \"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3\",\n  },\n  styles: [\n    {\n      types: [\"comment\", \"block-comment\", \"prolog\", \"doctype\", \"cdata\"],\n      style: {\n        color: \"#495495\",\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"punctuation\"],\n      style: {\n        color: \"#ccc\",\n      },\n    },\n    {\n      types: [\n        \"tag\",\n        \"attr-name\",\n        \"namespace\",\n        \"number\",\n        \"unit\",\n        \"hexcode\",\n        \"deleted\",\n      ],\n      style: {\n        color: \"#e2777a\",\n      },\n    },\n    {\n      types: [\"property\", \"selector\"],\n      style: {\n        color: \"#72f1b8\",\n        textShadow: \"0 0 2px #100c0f, 0 0 10px #257c5575, 0 0 35px #21272475\",\n      },\n    },\n    {\n      types: [\"function-name\"],\n      style: {\n        color: \"#6196cc\",\n      },\n    },\n    {\n      types: [\"boolean\", \"selector-id\", \"function\"],\n      style: {\n        color: \"#fdfdfd\",\n        textShadow:\n          \"0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975\",\n      },\n    },\n    {\n      types: [\"class-name\", \"maybe-class-name\", \"builtin\"],\n      style: {\n        color: \"#fff5f6\",\n        textShadow:\n          \"0 0 2px #000, 0 0 10px #fc1f2c75, 0 0 5px #fc1f2c75, 0 0 25px #fc1f2c75\",\n      },\n    },\n    {\n      types: [\"constant\", \"symbol\"],\n      style: {\n        color: \"#f92aad\",\n        textShadow: \"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3\",\n      },\n    },\n    {\n      types: [\"important\", \"atrule\", \"keyword\", \"selector-class\"],\n      style: {\n        color: \"#f4eee4\",\n        textShadow: \"0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575\",\n      },\n    },\n    {\n      types: [\"string\", \"char\", \"attr-value\", \"regex\", \"variable\"],\n      style: {\n        color: \"#f87c32\",\n      },\n    },\n    {\n      types: [\"parameter\"],\n      style: {\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"entity\", \"url\"],\n      style: {\n        color: \"#67cdcc\",\n      },\n    },\n    {\n      types: [\"operator\"],\n      style: {\n        color: \"ffffffee\",\n      },\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\",\n      },\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"entity\"],\n      style: {\n        cursor: \"help\",\n      },\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        color: \"green\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Original: https://github.com/damienstanton/ultramin\n// Converted automatically using ./tools/themeFromVsCode\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#282a2e\",\n    backgroundColor: \"#ffffff\",\n  },\n  styles: [\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(197, 200, 198)\",\n      },\n    },\n    {\n      types: [\"string\", \"number\", \"builtin\", \"variable\"],\n      style: {\n        color: \"rgb(150, 152, 150)\",\n      },\n    },\n    {\n      types: [\"class-name\", \"function\", \"tag\", \"attr-name\"],\n      style: {\n        color: \"rgb(40, 42, 46)\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Converted automatically using ./tools/themeFromVsCode\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#9CDCFE\",\n    backgroundColor: \"#1E1E1E\",\n  },\n  styles: [\n    {\n      types: [\"prolog\"],\n      style: {\n        color: \"rgb(0, 0, 128)\",\n      },\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(106, 153, 85)\",\n      },\n    },\n    {\n      types: [\"builtin\", \"changed\", \"keyword\", \"interpolation-punctuation\"],\n      style: {\n        color: \"rgb(86, 156, 214)\",\n      },\n    },\n    {\n      types: [\"number\", \"inserted\"],\n      style: {\n        color: \"rgb(181, 206, 168)\",\n      },\n    },\n    {\n      types: [\"constant\"],\n      style: {\n        color: \"rgb(100, 102, 149)\",\n      },\n    },\n    {\n      types: [\"attr-name\", \"variable\"],\n      style: {\n        color: \"rgb(156, 220, 254)\",\n      },\n    },\n    {\n      types: [\"deleted\", \"string\", \"attr-value\", \"template-punctuation\"],\n      style: {\n        color: \"rgb(206, 145, 120)\",\n      },\n    },\n    {\n      types: [\"selector\"],\n      style: {\n        color: \"rgb(215, 186, 125)\",\n      },\n    },\n    {\n      // Fix tag color\n      types: [\"tag\"],\n      style: {\n        color: \"rgb(78, 201, 176)\",\n      },\n    },\n    {\n      // Fix tag color for HTML\n      types: [\"tag\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"rgb(86, 156, 214)\",\n      },\n    },\n    {\n      types: [\"punctuation\", \"operator\"],\n      style: {\n        color: \"rgb(212, 212, 212)\",\n      },\n    },\n    {\n      // Fix punctuation color for HTML\n      types: [\"punctuation\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#808080\",\n      },\n    },\n    {\n      types: [\"function\"],\n      style: {\n        color: \"rgb(220, 220, 170)\",\n      },\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"rgb(78, 201, 176)\",\n      },\n    },\n    {\n      types: [\"char\"],\n      style: {\n        color: \"rgb(209, 105, 105)\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Converted automatically using ./tools/themeFromVsCode\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#000000\",\n    backgroundColor: \"#ffffff\",\n  },\n  styles: [\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"rgb(0, 128, 0)\",\n      },\n    },\n    {\n      types: [\"builtin\"],\n      style: {\n        color: \"rgb(0, 112, 193)\",\n      },\n    },\n    {\n      types: [\"number\", \"variable\", \"inserted\"],\n      style: {\n        color: \"rgb(9, 134, 88)\",\n      },\n    },\n    {\n      types: [\"operator\"],\n      style: {\n        color: \"rgb(0, 0, 0)\",\n      },\n    },\n    {\n      types: [\"constant\", \"char\"],\n      style: {\n        color: \"rgb(129, 31, 63)\",\n      },\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"rgb(128, 0, 0)\",\n      },\n    },\n    {\n      types: [\"attr-name\"],\n      style: {\n        color: \"rgb(255, 0, 0)\",\n      },\n    },\n    {\n      types: [\"deleted\", \"string\"],\n      style: {\n        color: \"rgb(163, 21, 21)\",\n      },\n    },\n    {\n      types: [\"changed\", \"punctuation\"],\n      style: {\n        color: \"rgb(4, 81, 165)\",\n      },\n    },\n    {\n      types: [\"function\", \"keyword\"],\n      style: {\n        color: \"rgb(0, 0, 255)\",\n      },\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"rgb(38, 127, 153)\",\n      },\n    },\n  ],\n}\nexport default theme\n", "//\n// dark version of code viewer styles built for https://jettwave.com\n// only uses colors found in default tailwindCSS => https://tailwindcss.com/docs/customizing-colors\n// designed by: https://github.com/ryanmogk\n//\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#f8fafc\",\n    backgroundColor: \"#011627\",\n  },\n  styles: [\n    {\n      types: [\"prolog\"],\n      style: {\n        color: \"#000080\",\n      },\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"#6A9955\",\n      },\n    },\n    {\n      types: [\"builtin\", \"changed\", \"keyword\", \"interpolation-punctuation\"],\n      style: {\n        color: \"#569CD6\",\n      },\n    },\n    {\n      types: [\"number\", \"inserted\"],\n      style: {\n        color: \"#B5CEA8\",\n      },\n    },\n    {\n      types: [\"constant\"],\n      style: {\n        color: \"#f8fafc\",\n      },\n    },\n    {\n      types: [\"attr-name\", \"variable\"],\n      style: {\n        color: \"#9CDCFE\",\n      },\n    },\n    {\n      types: [\"deleted\", \"string\", \"attr-value\", \"template-punctuation\"],\n      style: {\n        color: \"#cbd5e1\",\n      },\n    },\n    {\n      types: [\"selector\"],\n      style: {\n        color: \"#D7BA7D\",\n      },\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"#0ea5e9\",\n      },\n    },\n    {\n      types: [\"tag\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#0ea5e9\",\n      },\n    },\n    {\n      types: [\"punctuation\", \"operator\"],\n      style: {\n        color: \"#D4D4D4\",\n      },\n    },\n    {\n      types: [\"punctuation\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#808080\",\n      },\n    },\n    {\n      types: [\"function\"],\n      style: {\n        color: \"#7dd3fc\",\n      },\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"#0ea5e9\",\n      },\n    },\n    {\n      types: [\"char\"],\n      style: {\n        color: \"#D16969\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// light version of code viewer styles built for https://jettwave.com\n// only uses colors found in default tailwindCSS => https://tailwindcss.com/docs/customizing-colors\n// designed by: https://github.com/ryanmogk\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#0f172a\",\n    backgroundColor: \"#f1f5f9\",\n  },\n  styles: [\n    {\n      types: [\"prolog\"],\n      style: {\n        color: \"#000080\",\n      },\n    },\n    {\n      types: [\"comment\"],\n      style: {\n        color: \"#6A9955\",\n      },\n    },\n    {\n      types: [\"builtin\", \"changed\", \"keyword\", \"interpolation-punctuation\"],\n      style: {\n        color: \"#0c4a6e\",\n      },\n    },\n    {\n      types: [\"number\", \"inserted\"],\n      style: {\n        color: \"#B5CEA8\",\n      },\n    },\n    {\n      types: [\"constant\"],\n      style: {\n        color: \"#0f172a\",\n      },\n    },\n    {\n      types: [\"attr-name\", \"variable\"],\n      style: {\n        color: \"#0c4a6e\",\n      },\n    },\n    {\n      types: [\"deleted\", \"string\", \"attr-value\", \"template-punctuation\"],\n      style: {\n        color: \"#64748b\",\n      },\n    },\n    {\n      types: [\"selector\"],\n      style: {\n        color: \"#D7BA7D\",\n      },\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"#0ea5e9\",\n      },\n    },\n    {\n      types: [\"tag\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#0ea5e9\",\n      },\n    },\n    {\n      types: [\"punctuation\", \"operator\"],\n      style: {\n        color: \"#475569\",\n      },\n    },\n    {\n      types: [\"punctuation\"],\n      languages: [\"markup\"],\n      style: {\n        color: \"#808080\",\n      },\n    },\n    {\n      types: [\"function\"],\n      style: {\n        color: \"#0e7490\",\n      },\n    },\n    {\n      types: [\"class-name\"],\n      style: {\n        color: \"#0ea5e9\",\n      },\n    },\n    {\n      types: [\"char\"],\n      style: {\n        color: \"#D16969\",\n      },\n    },\n  ],\n}\nexport default theme\n", "/*\n    Adapted from the Prism One Dark Theme\n    https://github.com/PrismJS/prism-themes/blob/master/themes/prism-one-dark.css\n    Created by <PERSON> (@mrousavy) on 26.9.2023\n*/\nimport type { PrismTheme } from \"../types\"\n\nconst theme: PrismTheme = {\n  plain: {\n    backgroundColor: \"hsl(220, 13%, 18%)\",\n    color: \"hsl(220, 14%, 71%)\",\n    textShadow: \"0 1px rgba(0, 0, 0, 0.3)\",\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"cdata\"],\n      style: {\n        color: \"hsl(220, 10%, 40%)\",\n      },\n    },\n    {\n      types: [\"doctype\", \"punctuation\", \"entity\"],\n      style: {\n        color: \"hsl(220, 14%, 71%)\",\n      },\n    },\n    {\n      types: [\n        \"attr-name\",\n        \"class-name\",\n        \"maybe-class-name\",\n        \"boolean\",\n        \"constant\",\n        \"number\",\n        \"atrule\",\n      ],\n      style: { color: \"hsl(29, 54%, 61%)\" },\n    },\n    {\n      types: [\"keyword\"],\n      style: { color: \"hsl(286, 60%, 67%)\" },\n    },\n    {\n      types: [\"property\", \"tag\", \"symbol\", \"deleted\", \"important\"],\n      style: {\n        color: \"hsl(355, 65%, 65%)\",\n      },\n    },\n\n    {\n      types: [\n        \"selector\",\n        \"string\",\n        \"char\",\n        \"builtin\",\n        \"inserted\",\n        \"regex\",\n        \"attr-value\",\n      ],\n      style: {\n        color: \"hsl(95, 38%, 62%)\",\n      },\n    },\n    {\n      types: [\"variable\", \"operator\", \"function\"],\n      style: {\n        color: \"hsl(207, 82%, 66%)\",\n      },\n    },\n    {\n      types: [\"url\"],\n      style: {\n        color: \"hsl(187, 47%, 55%)\",\n      },\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        textDecorationLine: \"line-through\",\n      },\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        textDecorationLine: \"underline\",\n      },\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\",\n      },\n    },\n    {\n      types: [\"important\"],\n      style: {\n        color: \"hsl(220, 14%, 71%)\",\n      },\n    },\n  ],\n}\n\nexport default theme\n", "/*\n    Adapted from the Prism One Light Theme\n    https://github.com/PrismJS/prism-themes/blob/master/themes/prism-one-light.css\n    Created by <PERSON> (@mrousavy) on 26.9.2023\n*/\nimport type { PrismTheme } from \"../types\"\n\nconst theme: PrismTheme = {\n  plain: {\n    backgroundColor: \"hsl(230, 1%, 98%)\",\n    color: \"hsl(230, 8%, 24%)\",\n  },\n  styles: [\n    {\n      types: [\"comment\", \"prolog\", \"cdata\"],\n      style: {\n        color: \"hsl(230, 4%, 64%)\",\n      },\n    },\n    {\n      types: [\"doctype\", \"punctuation\", \"entity\"],\n      style: {\n        color: \"hsl(230, 8%, 24%)\",\n      },\n    },\n    {\n      types: [\n        \"attr-name\",\n        \"class-name\",\n        \"boolean\",\n        \"constant\",\n        \"number\",\n        \"atrule\",\n      ],\n      style: {\n        color: \"hsl(35, 99%, 36%)\",\n      },\n    },\n    {\n      types: [\"keyword\"],\n      style: {\n        color: \"hsl(301, 63%, 40%)\",\n      },\n    },\n\n    {\n      types: [\"property\", \"tag\", \"symbol\", \"deleted\", \"important\"],\n      style: {\n        color: \"hsl(5, 74%, 59%)\",\n      },\n    },\n    {\n      types: [\n        \"selector\",\n        \"string\",\n        \"char\",\n        \"builtin\",\n        \"inserted\",\n        \"regex\",\n        \"attr-value\",\n        \"punctuation\",\n      ],\n      style: {\n        color: \"hsl(119, 34%, 47%)\",\n      },\n    },\n    {\n      types: [\"variable\", \"operator\", \"function\"],\n      style: {\n        color: \"hsl(221, 87%, 60%)\",\n      },\n    },\n    {\n      types: [\"url\"],\n      style: {\n        color: \"hsl(198, 99%, 37%)\",\n      },\n    },\n    {\n      types: [\"deleted\"],\n      style: {\n        textDecorationLine: \"line-through\",\n      },\n    },\n    {\n      types: [\"inserted\"],\n      style: {\n        textDecorationLine: \"underline\",\n      },\n    },\n    {\n      types: [\"italic\"],\n      style: {\n        fontStyle: \"italic\",\n      },\n    },\n    {\n      types: [\"important\", \"bold\"],\n      style: {\n        fontWeight: \"bold\",\n      },\n    },\n    {\n      types: [\"important\"],\n      style: {\n        color: \"hsl(230, 8%, 24%)\",\n      },\n    },\n  ],\n}\n\nexport default theme\n", "// Gruvbox Material (dark)\n// Author: <PERSON><PERSON><PERSON> (https://github.com/sainnhe)\n// https://github.com/sainnhe/gruvbox-material\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#ebdbb2\",\n    backgroundColor: \"#292828\",\n  },\n  styles: [\n    {\n      types: [\n        \"imports\",\n        \"class-name\",\n        \"maybe-class-name\",\n        \"constant\",\n        \"doctype\",\n        \"builtin\",\n        \"function\",\n      ],\n      style: {\n        color: \"#d8a657\",\n      },\n    },\n    {\n      types: [\"property-access\"],\n      style: {\n        color: \"#7daea3\",\n      },\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"#e78a4e\",\n      },\n    },\n    {\n      types: [\"attr-name\", \"char\", \"url\", \"regex\"],\n      style: {\n        color: \"#a9b665\",\n      },\n    },\n    {\n      types: [\"attr-value\", \"string\"],\n      style: {\n        color: \"#89b482\",\n      },\n    },\n    {\n      types: [\"comment\", \"prolog\", \"cdata\", \"operator\", \"inserted\"],\n      style: {\n        color: \"#a89984\",\n      },\n    },\n    {\n      types: [\n        \"delimiter\",\n        \"boolean\",\n        \"keyword\",\n        \"selector\",\n        \"important\",\n        \"atrule\",\n        \"property\",\n        \"variable\",\n        \"deleted\",\n      ],\n      style: {\n        color: \"#ea6962\",\n      },\n    },\n    {\n      types: [\"entity\", \"number\", \"symbol\"],\n      style: {\n        color: \"#d3869b\",\n      },\n    },\n  ],\n}\nexport default theme\n", "// Gruvbox Material (light)\n// Author: <PERSON><PERSON><PERSON> (https://github.com/sainnhe)\n// https://github.com/sainnhe/gruvbox-material\nimport type { PrismTheme } from \"../types\"\nconst theme: PrismTheme = {\n  plain: {\n    color: \"#654735\",\n    backgroundColor: \"#f9f5d7\",\n  },\n  styles: [\n    {\n      types: [\n        \"delimiter\",\n        \"boolean\",\n        \"keyword\",\n        \"selector\",\n        \"important\",\n        \"atrule\",\n        \"property\",\n        \"variable\",\n        \"deleted\",\n      ],\n      style: {\n        color: \"#af2528\",\n      },\n    },\n    {\n      types: [\n        \"imports\",\n        \"class-name\",\n        \"maybe-class-name\",\n        \"constant\",\n        \"doctype\",\n        \"builtin\",\n      ],\n      style: {\n        color: \"#b4730e\",\n      },\n    },\n    {\n      types: [\"string\", \"attr-value\"],\n      style: {\n        color: \"#477a5b\",\n      },\n    },\n    {\n      types: [\"property-access\"],\n      style: {\n        color: \"#266b79\",\n      },\n    },\n    {\n      types: [\"function\", \"attr-name\", \"char\", \"url\"],\n      style: {\n        color: \"#72761e\",\n      },\n    },\n    {\n      types: [\"tag\"],\n      style: {\n        color: \"#b94c07\",\n      },\n    },\n    {\n      types: [\"comment\", \"prolog\", \"cdata\", \"operator\", \"inserted\"],\n      style: {\n        color: \"#a89984\",\n      },\n    },\n    {\n      types: [\"entity\", \"number\", \"symbol\"],\n      style: {\n        color: \"#924f79\",\n      },\n    },\n  ],\n}\nexport default theme\n", "import { ThemeDict } from \"../utils/themeToDict\"\nimport { useCallback } from \"react\"\nimport { LineInputProps, LineOutputProps } from \"../types\"\nimport clsx from \"clsx\"\n\nexport const useGetLineProps = (themeDictionary?: ThemeDict) =>\n  useCallback(\n    ({ className, style, line, ...rest }: LineInputProps) => {\n      const output: LineOutputProps = {\n        ...rest,\n        className: clsx(\"token-line\", className),\n      }\n\n      if (typeof themeDictionary === \"object\" && \"plain\" in themeDictionary)\n        output.style = themeDictionary.plain\n\n      if (typeof style === \"object\")\n        output.style = { ...(output.style || {}), ...style }\n\n      return output\n    },\n    [themeDictionary]\n  )\n", "import { ThemeDict } from \"../utils/themeToDict\"\nimport { CSSProperties, useCallback } from \"react\"\nimport { Token, TokenInputProps, TokenOutputProps } from \"../types\"\nimport clsx from \"clsx\"\n\nexport const useGetTokenProps = (themeDictionary?: ThemeDict) => {\n  const styleForToken = useCallback(\n    ({ types, empty }: Token) => {\n      if (themeDictionary == null) return undefined\n      else if (types.length === 1 && types[0] === \"plain\") {\n        return empty != null ? { display: \"inline-block\" } : undefined\n      } else if (types.length === 1 && empty != null) {\n        return themeDictionary[types[0]]\n      }\n\n      return Object.assign(\n        empty != null ? { display: \"inline-block\" } : {},\n        ...types.map(type => themeDictionary[type])\n      ) satisfies CSSProperties\n    },\n    [themeDictionary]\n  )\n\n  return useCallback(\n    ({ token, className, style, ...rest }: TokenInputProps) => {\n      const output: TokenOutputProps = {\n        ...rest,\n        className: clsx(\"token\", ...token.types, className),\n        children: token.content,\n        style: styleForToken(token),\n      }\n\n      if (style != null) {\n        output.style = {\n          ...(output.style || {}),\n          ...style,\n        }\n      }\n\n      return output\n    },\n    [styleForToken]\n  )\n}\n", "import type { Token } from \"../types\"\nimport type { Token as PrismToken, TokenStream } from \"prismjs\"\n\nconst newlineRe = /\\r\\n|\\r|\\n/\n\n// Empty lines need to contain a single empty token, denoted with { empty: true }\nconst normalizeEmptyLines = (line: Token[]) => {\n  if (line.length === 0) {\n    line.push({\n      types: [\"plain\"],\n      content: \"\\n\",\n      empty: true,\n    })\n  } else if (line.length === 1 && line[0].content === \"\") {\n    line[0].content = \"\\n\"\n    line[0].empty = true\n  }\n}\n\nconst appendTypes = (types: string[], add: string[] | string): string[] => {\n  const typesSize = types.length\n\n  if (typesSize > 0 && types[typesSize - 1] === add) {\n    return types\n  }\n\n  return types.concat(add)\n}\n\n// Takes an array of Prism's tokens and groups them by line, turning plain\n// strings into tokens as well. Tokens can become recursive in some cases,\n// which means that their types are concatenated. Plain-string tokens however\n// are always of type \"plain\".\n// This is not recursive to avoid exceeding the call-stack limit, since it's unclear\n// how nested Prism's tokens can become\nconst normalizeTokens = (tokens: (PrismToken | string)[]): Token[][] => {\n  const typeArrStack: string[][] = [[]]\n  const tokenArrStack = [tokens]\n  const tokenArrIndexStack = [0]\n  const tokenArrSizeStack = [tokens.length]\n  let i = 0\n  let stackIndex = 0\n  let currentLine: Token[] = []\n  const acc = [currentLine]\n\n  while (stackIndex > -1) {\n    while (\n      (i = tokenArrIndexStack[stackIndex]++) < tokenArrSizeStack[stackIndex]\n    ) {\n      let content: TokenStream\n      let types = typeArrStack[stackIndex]\n      const tokenArr = tokenArrStack[stackIndex]\n      const token = tokenArr[i]\n\n      // Determine content and append type to types if necessary\n      if (typeof token === \"string\") {\n        types = stackIndex > 0 ? types : [\"plain\"]\n        content = token\n      } else {\n        types = appendTypes(types, token.type)\n\n        if (token.alias) {\n          types = appendTypes(types, token.alias)\n        }\n\n        content = token.content\n      }\n\n      // If token.content is an array, increase the stack depth and repeat this while-loop\n      if (typeof content !== \"string\") {\n        stackIndex++\n        typeArrStack.push(types)\n        tokenArrStack.push(content as PrismToken[])\n        tokenArrIndexStack.push(0)\n        tokenArrSizeStack.push(content.length)\n        continue\n      }\n\n      // Split by newlines\n      const splitByNewlines = content.split(newlineRe)\n      const newlineCount = splitByNewlines.length\n      currentLine.push({\n        types,\n        content: splitByNewlines[0],\n      })\n\n      // Create a new line for each string on a new line\n      for (let i = 1; i < newlineCount; i++) {\n        normalizeEmptyLines(currentLine)\n        acc.push((currentLine = []))\n        currentLine.push({\n          types,\n          content: splitByNewlines[i],\n        })\n      }\n    }\n\n    // Decreate the stack depth\n    stackIndex--\n    typeArrStack.pop()\n    tokenArrStack.pop()\n    tokenArrIndexStack.pop()\n    tokenArrSizeStack.pop()\n  }\n\n  normalizeEmptyLines(currentLine)\n  return acc\n}\n\nexport default normalizeTokens\n", "import { EnvConfig, Language, PrismGrammar, PrismLib } from \"../types\"\nimport normalizeTokens from \"../utils/normalizeTokens\"\nimport { useMemo } from \"react\"\n\ntype Options = {\n  prism: PrismLib\n  code: string\n  grammar?: PrismGrammar\n  language: Language\n}\n\nexport const useTokenize = ({ prism, code, grammar, language }: Options) => {\n  return useMemo(() => {\n    if (grammar == null) return normalizeTokens([code])\n\n    const prismConfig: EnvConfig = {\n      code,\n      grammar,\n      language,\n      tokens: [],\n    }\n\n    prism.hooks.run(\"before-tokenize\", prismConfig)\n    prismConfig.tokens = prism.tokenize(code, grammar)\n    prism.hooks.run(\"after-tokenize\", prismConfig)\n    return normalizeTokens(prismConfig.tokens)\n  }, [\n    code,\n    grammar,\n    language,\n    // prism is a stable import\n    prism,\n  ])\n}\n", "import type { Language, StyleObj, PrismTheme } from \"../types\"\nexport type ThemeDict = {\n  root: StyleObj\n  plain: StyleObj\n  [type: string]: StyleObj\n}\n\nconst themeToDict = (theme: PrismTheme, language: Language): ThemeDict => {\n  const { plain } = theme\n  const themeDict = theme.styles.reduce<ThemeDict>((acc, themeEntry) => {\n    const { languages, style } = themeEntry\n\n    if (languages && !languages.includes(language)) {\n      return acc\n    }\n\n    themeEntry.types.forEach(type => {\n      const accStyle: StyleObj = { ...acc[type], ...style }\n      acc[type] = accStyle\n    })\n    return acc\n  }, {} as ThemeDict)\n\n  themeDict.root = plain as StyleObj\n  themeDict.plain = { ...plain, backgroundColor: undefined } as StyleObj\n  return themeDict\n}\n\nexport default themeToDict\n", "import { InternalHighlightProps } from \"../types\"\nimport { useGetLineProps } from \"./useGetLineProps\"\nimport { useGetTokenProps } from \"./useGetTokenProps\"\nimport { useTokenize } from \"./useTokenize\"\nimport themeToDict from \"../utils/themeToDict\"\n\nexport const Highlight = ({\n  children,\n  language: _language,\n  code,\n  theme,\n  prism,\n}: InternalHighlightProps) => {\n  const language = _language.toLowerCase()\n  const themeDictionary = themeToDict(theme, language)\n  const getLineProps = useGetLineProps(themeDictionary)\n  const getTokenProps = useGetTokenProps(themeDictionary)\n  const grammar = prism.languages[language]\n  const tokens = useTokenize({ prism, language, code, grammar })\n\n  return children({\n    tokens,\n    className: `prism-code language-${language}`,\n    style: themeDictionary != null ? themeDictionary.root : {},\n    getLineProps,\n    getTokenProps,\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,yHAAAA,SAAA;AAeA,QAAIC,SAAS,WAAY;AAGxB,UAAI,OAAO;AACX,UAAI,WAAW;AAGf,UAAI,mBAAmB,CAAC;AAGxB,UAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUP,MAAM;AAAA,UACL,QAAQ,SAAS,OAAO,QAAQ;AAC/B,gBAAI,kBAAkB,OAAO;AAC5B,qBAAO,IAAI,MAAM,OAAO,MAAM,OAAO,OAAO,OAAO,GAAG,OAAO,KAAK;AAAA,YACnE,WAAW,MAAM,QAAQ,MAAM,GAAG;AACjC,qBAAO,OAAO,IAAI,MAAM;AAAA,YACzB,OAAO;AACN,qBAAO,OAAO,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,WAAW,GAAG;AAAA,YAClF;AAAA,UACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAkBA,MAAM,SAAU,GAAG;AAClB,mBAAO,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,UACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,OAAO,SAAU,KAAK;AACrB,gBAAI,CAAC,IAAI,MAAM,GAAG;AACjB,qBAAO,eAAe,KAAK,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAAA,YACzD;AACA,mBAAO,IAAI,MAAM;AAAA,UAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAYA,OAAO,SAAS,UAAU,GAAG,SAAS;AACrC,sBAAU,WAAW,CAAC;AAEtB,gBAAI;AAAO,gBAAI;AACf,oBAAQ,EAAE,KAAK,KAAK,CAAC,GAAG;AAAA,cACvB,KAAK;AACJ,qBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,oBAAI,QAAQ,EAAE,GAAG;AAChB,yBAAO,QAAQ,EAAE;AAAA,gBAClB;AACA;AAAA,gBAA4C,CAAC;AAC7C,wBAAQ,EAAE,IAAI;AAEd,yBAAS,OAAO,GAAG;AAClB,sBAAI,EAAE,eAAe,GAAG,GAAG;AAC1B,0BAAM,GAAG,IAAI,UAAU,EAAE,GAAG,GAAG,OAAO;AAAA,kBACvC;AAAA,gBACD;AAEA;AAAA;AAAA,kBAA2B;AAAA;AAAA,cAE5B,KAAK;AACJ,qBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,oBAAI,QAAQ,EAAE,GAAG;AAChB,yBAAO,QAAQ,EAAE;AAAA,gBAClB;AACA,wBAAQ,CAAC;AACT,wBAAQ,EAAE,IAAI;AAEd;AAAA;AAAA,gBAAyC,EAAK,QAAQ,SAAU,GAAG,GAAG;AACrE,wBAAM,CAAC,IAAI,UAAU,GAAG,OAAO;AAAA,gBAChC,CAAC;AAED;AAAA;AAAA,kBAA2B;AAAA;AAAA,cAE5B;AACC,uBAAO;AAAA,YACT;AAAA,UACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAUA,aAAa,SAAU,SAAS;AAC/B,mBAAO,SAAS;AACf,kBAAI,IAAI,KAAK,KAAK,QAAQ,SAAS;AACnC,kBAAI,GAAG;AACN,uBAAO,EAAE,CAAC,EAAE,YAAY;AAAA,cACzB;AACA,wBAAU,QAAQ;AAAA,YACnB;AACA,mBAAO;AAAA,UACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASA,aAAa,SAAU,SAAS,UAAU;AAGzC,oBAAQ,YAAY,QAAQ,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,EAAE;AAIpE,oBAAQ,UAAU,IAAI,cAAc,QAAQ;AAAA,UAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAqBA,UAAU,SAAU,SAAS,WAAW,mBAAmB;AAC1D,gBAAI,KAAK,QAAQ;AAEjB,mBAAO,SAAS;AACf,kBAAI,YAAY,QAAQ;AACxB,kBAAI,UAAU,SAAS,SAAS,GAAG;AAClC,uBAAO;AAAA,cACR;AACA,kBAAI,UAAU,SAAS,EAAE,GAAG;AAC3B,uBAAO;AAAA,cACR;AACA,wBAAU,QAAQ;AAAA,YACnB;AACA,mBAAO,CAAC,CAAC;AAAA,UACV;AAAA,QACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA,WAAW;AAAA;AAAA;AAAA;AAAA,UAIV,OAAO;AAAA,UACP,WAAW;AAAA,UACX,MAAM;AAAA,UACN,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UA8BL,QAAQ,SAAU,IAAI,OAAO;AAC5B,gBAAIC,QAAO,EAAE,KAAK,MAAM,EAAE,UAAU,EAAE,CAAC;AAEvC,qBAAS,OAAO,OAAO;AACtB,cAAAA,MAAK,GAAG,IAAI,MAAM,GAAG;AAAA,YACtB;AAEA,mBAAOA;AAAA,UACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UA6EA,cAAc,SAAU,QAAQ,QAAQ,QAAQ,MAAM;AACrD,mBAAO;AAAA,YAA4B,EAAE;AACrC,gBAAI,UAAU,KAAK,MAAM;AAEzB,gBAAI,MAAM,CAAC;AAEX,qBAAS,SAAS,SAAS;AAC1B,kBAAI,QAAQ,eAAe,KAAK,GAAG;AAElC,oBAAI,SAAS,QAAQ;AACpB,2BAAS,YAAY,QAAQ;AAC5B,wBAAI,OAAO,eAAe,QAAQ,GAAG;AACpC,0BAAI,QAAQ,IAAI,OAAO,QAAQ;AAAA,oBAChC;AAAA,kBACD;AAAA,gBACD;AAGA,oBAAI,CAAC,OAAO,eAAe,KAAK,GAAG;AAClC,sBAAI,KAAK,IAAI,QAAQ,KAAK;AAAA,gBAC3B;AAAA,cACD;AAAA,YACD;AAEA,gBAAI,MAAM,KAAK,MAAM;AACrB,iBAAK,MAAM,IAAI;AAGf,cAAE,UAAU,IAAI,EAAE,WAAW,SAAU,KAAK,OAAO;AAClD,kBAAI,UAAU,OAAO,OAAO,QAAQ;AACnC,qBAAK,GAAG,IAAI;AAAA,cACb;AAAA,YACD,CAAC;AAED,mBAAO;AAAA,UACR;AAAA;AAAA,UAGA,KAAK,SAAS,IAAI,GAAG,UAAU,MAAM,SAAS;AAC7C,sBAAU,WAAW,CAAC;AAEtB,gBAAI,QAAQ,EAAE,KAAK;AAEnB,qBAAS,KAAK,GAAG;AAChB,kBAAI,EAAE,eAAe,CAAC,GAAG;AACxB,yBAAS,KAAK,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC;AAEnC,oBAAI,WAAW,EAAE,CAAC;AAClB,oBAAI,eAAe,EAAE,KAAK,KAAK,QAAQ;AAEvC,oBAAI,iBAAiB,YAAY,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AAC3D,0BAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,sBAAI,UAAU,UAAU,MAAM,OAAO;AAAA,gBACtC,WAAW,iBAAiB,WAAW,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AACjE,0BAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,sBAAI,UAAU,UAAU,GAAG,OAAO;AAAA,gBACnC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QAEA,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAsBV,WAAW,SAAU,MAAM,SAAS,UAAU;AAC7C,cAAI,MAAM;AAAA,YACT,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACD;AACA,YAAE,MAAM,IAAI,mBAAmB,GAAG;AAClC,cAAI,CAAC,IAAI,SAAS;AACjB,kBAAM,IAAI,MAAM,mBAAmB,IAAI,WAAW,mBAAmB;AAAA,UACtE;AACA,cAAI,SAAS,EAAE,SAAS,IAAI,MAAM,IAAI,OAAO;AAC7C,YAAE,MAAM,IAAI,kBAAkB,GAAG;AACjC,iBAAO,MAAM,UAAU,EAAE,KAAK,OAAO,IAAI,MAAM,GAAG,IAAI,QAAQ;AAAA,QAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QA0BA,UAAU,SAAU,MAAM,SAAS;AAClC,cAAI,OAAO,QAAQ;AACnB,cAAI,MAAM;AACT,qBAAS,SAAS,MAAM;AACvB,sBAAQ,KAAK,IAAI,KAAK,KAAK;AAAA,YAC5B;AAEA,mBAAO,QAAQ;AAAA,UAChB;AAEA,cAAI,YAAY,IAAI,WAAW;AAC/B,mBAAS,WAAW,UAAU,MAAM,IAAI;AAExC,uBAAa,MAAM,WAAW,SAAS,UAAU,MAAM,CAAC;AAExD,iBAAO,QAAQ,SAAS;AAAA,QACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,OAAO;AAAA,UACN,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAcN,KAAK,SAAU,MAAM,UAAU;AAC9B,gBAAIC,SAAQ,EAAE,MAAM;AAEpB,YAAAA,OAAM,IAAI,IAAIA,OAAM,IAAI,KAAK,CAAC;AAE9B,YAAAA,OAAM,IAAI,EAAE,KAAK,QAAQ;AAAA,UAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWA,KAAK,SAAU,MAAM,KAAK;AACzB,gBAAI,YAAY,EAAE,MAAM,IAAI,IAAI;AAEhC,gBAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACpC;AAAA,YACD;AAEA,qBAAS,IAAI,GAAG,UAAW,WAAW,UAAU,GAAG,KAAK;AACvD,uBAAS,GAAG;AAAA,YACb;AAAA,UACD;AAAA,QACD;AAAA,QAEA;AAAA,MACD;AAkBA,eAAS,MAAM,MAAM,SAAS,OAAO,YAAY;AAUhD,aAAK,OAAO;AASZ,aAAK,UAAU;AAQf,aAAK,QAAQ;AAEb,aAAK,UAAU,cAAc,IAAI,SAAS;AAAA,MAC3C;AA8BA,YAAM,YAAY,SAAS,UAAU,GAAG,UAAU;AACjD,YAAI,OAAO,KAAK,UAAU;AACzB,iBAAO;AAAA,QACR;AACA,YAAI,MAAM,QAAQ,CAAC,GAAG;AACrB,cAAI,IAAI;AACR,YAAE,QAAQ,SAAU,GAAG;AACtB,iBAAK,UAAU,GAAG,QAAQ;AAAA,UAC3B,CAAC;AACD,iBAAO;AAAA,QACR;AAEA,YAAI,MAAM;AAAA,UACT,MAAM,EAAE;AAAA,UACR,SAAS,UAAU,EAAE,SAAS,QAAQ;AAAA,UACtC,KAAK;AAAA,UACL,SAAS,CAAC,SAAS,EAAE,IAAI;AAAA,UACzB,YAAY,CAAC;AAAA,UACb;AAAA,QACD;AAEA,YAAI,UAAU,EAAE;AAChB,YAAI,SAAS;AACZ,cAAI,MAAM,QAAQ,OAAO,GAAG;AAC3B,kBAAM,UAAU,KAAK,MAAM,IAAI,SAAS,OAAO;AAAA,UAChD,OAAO;AACN,gBAAI,QAAQ,KAAK,OAAO;AAAA,UACzB;AAAA,QACD;AAEA,UAAE,MAAM,IAAI,QAAQ,GAAG;AAEvB,YAAI,aAAa;AACjB,iBAAS,QAAQ,IAAI,YAAY;AAChC,wBAAc,MAAM,OAAO,QAAQ,IAAI,WAAW,IAAI,KAAK,IAAI,QAAQ,MAAM,QAAQ,IAAI;AAAA,QAC1F;AAEA,eAAO,MAAM,IAAI,MAAM,aAAa,IAAI,QAAQ,KAAK,GAAG,IAAI,MAAM,aAAa,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM;AAAA,MACrH;AASA,eAAS,aAAa,SAAS,KAAK,MAAM,YAAY;AACrD,gBAAQ,YAAY;AACpB,YAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,YAAI,SAAS,cAAc,MAAM,CAAC,GAAG;AAEpC,cAAI,mBAAmB,MAAM,CAAC,EAAE;AAChC,gBAAM,SAAS;AACf,gBAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,gBAAgB;AAAA,QAC3C;AACA,eAAO;AAAA,MACR;AAgBA,eAAS,aAAa,MAAM,WAAW,SAAS,WAAW,UAAU,SAAS;AAC7E,iBAAS,SAAS,SAAS;AAC1B,cAAI,CAAC,QAAQ,eAAe,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;AACtD;AAAA,UACD;AAEA,cAAI,WAAW,QAAQ,KAAK;AAC5B,qBAAW,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAEzD,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACzC,gBAAI,WAAW,QAAQ,SAAS,QAAQ,MAAM,GAAG;AAChD;AAAA,YACD;AAEA,gBAAI,aAAa,SAAS,CAAC;AAC3B,gBAAI,SAAS,WAAW;AACxB,gBAAI,aAAa,CAAC,CAAC,WAAW;AAC9B,gBAAI,SAAS,CAAC,CAAC,WAAW;AAC1B,gBAAI,QAAQ,WAAW;AAEvB,gBAAI,UAAU,CAAC,WAAW,QAAQ,QAAQ;AAEzC,kBAAI,QAAQ,WAAW,QAAQ,SAAS,EAAE,MAAM,WAAW,EAAE,CAAC;AAC9D,yBAAW,UAAU,OAAO,WAAW,QAAQ,QAAQ,QAAQ,GAAG;AAAA,YACnE;AAGA,gBAAI,UAAU,WAAW,WAAW;AAEpC,qBACK,cAAc,UAAU,MAAM,MAAM,UACxC,gBAAgB,UAAU,MAC1B,OAAO,YAAY,MAAM,QAAQ,cAAc,YAAY,MAC1D;AAED,kBAAI,WAAW,OAAO,QAAQ,OAAO;AACpC;AAAA,cACD;AAEA,kBAAI,MAAM,YAAY;AAEtB,kBAAI,UAAU,SAAS,KAAK,QAAQ;AAEnC;AAAA,cACD;AAEA,kBAAI,eAAe,OAAO;AACzB;AAAA,cACD;AAEA,kBAAI,cAAc;AAClB,kBAAI;AAEJ,kBAAI,QAAQ;AACX,wBAAQ,aAAa,SAAS,KAAK,MAAM,UAAU;AACnD,oBAAI,CAAC,SAAS,MAAM,SAAS,KAAK,QAAQ;AACzC;AAAA,gBACD;AAEA,oBAAI,OAAO,MAAM;AACjB,oBAAI,KAAK,MAAM,QAAQ,MAAM,CAAC,EAAE;AAChC,oBAAI,IAAI;AAGR,qBAAK,YAAY,MAAM;AACvB,uBAAO,QAAQ,GAAG;AACjB,gCAAc,YAAY;AAC1B,uBAAK,YAAY,MAAM;AAAA,gBACxB;AAEA,qBAAK,YAAY,MAAM;AACvB,sBAAM;AAGN,oBAAI,YAAY,iBAAiB,OAAO;AACvC;AAAA,gBACD;AAGA,yBACK,IAAI,aACR,MAAM,UAAU,SAAS,IAAI,MAAM,OAAO,EAAE,UAAU,WACtD,IAAI,EAAE,MACL;AACD;AACA,uBAAK,EAAE,MAAM;AAAA,gBACd;AACA;AAGA,sBAAM,KAAK,MAAM,KAAK,CAAC;AACvB,sBAAM,SAAS;AAAA,cAChB,OAAO;AACN,wBAAQ,aAAa,SAAS,GAAG,KAAK,UAAU;AAChD,oBAAI,CAAC,OAAO;AACX;AAAA,gBACD;AAAA,cACD;AAGA,kBAAI,OAAO,MAAM;AACjB,kBAAI,WAAW,MAAM,CAAC;AACtB,kBAAI,SAAS,IAAI,MAAM,GAAG,IAAI;AAC9B,kBAAI,QAAQ,IAAI,MAAM,OAAO,SAAS,MAAM;AAE5C,kBAAI,QAAQ,MAAM,IAAI;AACtB,kBAAI,WAAW,QAAQ,QAAQ,OAAO;AACrC,wBAAQ,QAAQ;AAAA,cACjB;AAEA,kBAAI,aAAa,YAAY;AAE7B,kBAAI,QAAQ;AACX,6BAAa,SAAS,WAAW,YAAY,MAAM;AACnD,uBAAO,OAAO;AAAA,cACf;AAEA,0BAAY,WAAW,YAAY,WAAW;AAE9C,kBAAI,UAAU,IAAI,MAAM,OAAO,SAAS,EAAE,SAAS,UAAU,MAAM,IAAI,UAAU,OAAO,QAAQ;AAChG,4BAAc,SAAS,WAAW,YAAY,OAAO;AAErD,kBAAI,OAAO;AACV,yBAAS,WAAW,aAAa,KAAK;AAAA,cACvC;AAEA,kBAAI,cAAc,GAAG;AAKpB,oBAAI,gBAAgB;AAAA,kBACnB,OAAO,QAAQ,MAAM;AAAA,kBACrB;AAAA,gBACD;AACA,6BAAa,MAAM,WAAW,SAAS,YAAY,MAAM,KAAK,aAAa;AAG3E,oBAAI,WAAW,cAAc,QAAQ,QAAQ,OAAO;AACnD,0BAAQ,QAAQ,cAAc;AAAA,gBAC/B;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAeA,eAAS,aAAa;AAErB,YAAI,OAAO,EAAE,OAAO,MAAM,MAAM,MAAM,MAAM,KAAK;AAEjD,YAAI,OAAO,EAAE,OAAO,MAAM,MAAM,MAAM,MAAM,KAAK;AACjD,aAAK,OAAO;AAGZ,aAAK,OAAO;AAEZ,aAAK,OAAO;AACZ,aAAK,SAAS;AAAA,MACf;AAWA,eAAS,SAAS,MAAM,MAAM,OAAO;AAEpC,YAAI,OAAO,KAAK;AAEhB,YAAI,UAAU,EAAE,OAAc,MAAM,MAAM,KAAW;AACrD,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK;AAEL,eAAO;AAAA,MACR;AASA,eAAS,YAAY,MAAM,MAAM,OAAO;AACvC,YAAI,OAAO,KAAK;AAChB,iBAAS,IAAI,GAAG,IAAI,SAAS,SAAS,KAAK,MAAM,KAAK;AACrD,iBAAO,KAAK;AAAA,QACb;AACA,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,UAAU;AAAA,MAChB;AAMA,eAAS,QAAQ,MAAM;AACtB,YAAI,QAAQ,CAAC;AACb,YAAI,OAAO,KAAK,KAAK;AACrB,eAAO,SAAS,KAAK,MAAM;AAC1B,gBAAM,KAAK,KAAK,KAAK;AACrB,iBAAO,KAAK;AAAA,QACb;AACA,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACR,EAAE;AAEF,IAAAH,QAAO,UAAUC;AACjB,IAAAA,OAAM,UAAUA;AAAA;AAAA;;;ACt4BhB;AAAA;AAAA,mBAAAG;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,YAAuB;AACA,gBAAU,SAAO,EAAC,SAAQ,EAAC,SAAQ,+BAA8B,QAAO,KAAE,GAAE,QAAO,EAAC,SAAQ,kBAAiB,QAAO,KAAE,GAAE,SAAQ,EAAC,SAAQ,wHAAuH,QAAO,MAAG,QAAO,EAAC,mBAAkB,EAAC,SAAQ,8BAA6B,YAAW,MAAG,QAAO,MAAG,QAAO,KAAI,GAAE,QAAO,EAAC,SAAQ,mBAAkB,QAAO,KAAE,GAAE,aAAY,gBAAe,eAAc,aAAY,MAAK,aAAY,EAAC,GAAE,OAAM,EAAC,SAAQ,6BAA4B,QAAO,KAAE,GAAE,KAAI,EAAC,SAAQ,wHAAuH,QAAO,MAAG,QAAO,EAAC,KAAI,EAAC,SAAQ,kBAAiB,QAAO,EAAC,aAAY,SAAQ,WAAU,eAAc,EAAC,GAAE,gBAAe,CAAC,GAAE,cAAa,EAAC,SAAQ,sCAAqC,QAAO,EAAC,aAAY,CAAC,EAAC,SAAQ,MAAK,OAAM,cAAa,GAAE,EAAC,SAAQ,oBAAmB,YAAW,KAAE,CAAC,EAAC,EAAC,GAAE,aAAY,QAAO,aAAY,EAAC,SAAQ,aAAY,QAAO,EAAC,WAAU,eAAc,EAAC,EAAC,EAAC,GAAE,QAAO,CAAC,EAAC,SAAQ,mBAAkB,OAAM,eAAc,GAAE,oBAAoB,EAAC,GAAQ,gBAAU,OAAO,IAAI,OAAO,YAAY,EAAE,OAAO,SAAa,gBAAU,OAAO,QAAa,gBAAU,OAAO,QAAQ,OAAO,iBAAiB,EAAE,SAAa,gBAAU,QAAa,YAAM,IAAI,QAAO,SAAS,GAAE;AAAC,eAAW,EAAE,SAAO,EAAE,WAAW,QAAM,EAAE,QAAQ,QAAQ,SAAQ,GAAG;AAAE,CAAC,GAAE,OAAO,eAAqB,gBAAU,OAAO,KAAI,cAAa,EAAC,OAAM,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,CAAC,GAAE,KAAG,EAAE,cAAY,CAAC,IAAE,EAAC,SAAQ,qCAAoC,YAAW,MAAG,QAAa,gBAAU,CAAC,EAAC,GAAE,EAAE,QAAM,wBAAuB,EAAC,kBAAiB,EAAC,SAAQ,6BAA4B,QAAO,EAAC,EAAC,IAAG,KAAG,EAAE,cAAY,CAAC,IAAE,EAAC,SAAQ,WAAU,QAAa,gBAAU,CAAC,EAAC,GAAE,CAAC;AAAG,IAAE,CAAC,IAAE,EAAC,SAAQ,OAAO,wFAAwF,OAAO,QAAQ,OAAM,WAAU;AAAC,WAAO;AAAA,EAAC,CAAC,GAAE,GAAG,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,GAAQ,gBAAU,aAAa,UAAS,SAAQ,CAAC;AAAC,EAAC,CAAC,GAAE,OAAO,eAAqB,gBAAU,OAAO,KAAI,gBAAe,EAAC,OAAM,SAAS,GAAE,GAAE;AAAC,EAAM,gBAAU,OAAO,IAAI,OAAO,cAAc,EAAE,KAAK,EAAC,SAAQ,OAAO,aAAa,SAAO,QAAM,IAAE,MAAI,iDAAiD,QAAO,GAAG,GAAE,YAAW,MAAG,QAAO,EAAC,aAAY,YAAW,cAAa,EAAC,SAAQ,YAAW,QAAO,EAAC,OAAM,EAAC,SAAQ,0CAAyC,YAAW,MAAG,OAAM,CAAC,GAAE,cAAY,CAAC,GAAE,QAAa,gBAAU,CAAC,EAAC,GAAE,aAAY,CAAC,EAAC,SAAQ,MAAK,OAAM,cAAa,GAAE,KAAK,EAAC,EAAC,EAAC,EAAC,CAAC;AAAC,EAAC,CAAC,GAAQ,gBAAU,OAAW,gBAAU,QAAa,gBAAU,SAAa,gBAAU,QAAa,gBAAU,MAAU,gBAAU,QAAa,gBAAU,MAAU,gBAAU,OAAO,UAAS,CAAC,CAAC,GAAQ,gBAAU,OAAW,gBAAU,KAAU,gBAAU,OAAW,gBAAU,KAAU,gBAAU,MAAU,gBAAU,KAAI,SAAS,GAAE;AAAC,MAAI,IAAE,EAAC,SAAQ,wBAAuB,OAAM,SAAQ,GAAE,IAAE,8FAA6F,IAAE,iBAAe,EAAE,SAAO,KAAI,IAAE,OAAO,IAAE,MAAI,CAAC,GAAE,IAAE,EAAC,SAAQ,yBAAwB,YAAW,MAAG,OAAM,WAAU;AAAE,IAAE,UAAU,QAAM,EAAC,cAAa,EAAC,SAAQ,mDAAkD,YAAW,MAAG,QAAO,EAAC,uBAAsB,EAAC,SAAQ,WAAU,YAAW,MAAG,OAAM,WAAU,GAAE,0BAAyB,EAAC,SAAQ,WAAU,OAAM,cAAa,GAAE,OAAM,EAAC,SAAQ,GAAE,QAAO,EAAC,QAAO,GAAE,qBAAoB,EAAC,SAAQ,KAAI,OAAM,WAAU,EAAC,EAAC,GAAE,kBAAiB,GAAE,YAAW,EAAC,SAAQ,0BAAyB,OAAM,aAAY,GAAE,QAAO,EAAC,EAAC,GAAE,kBAAiB,GAAE,YAAW,EAAC,SAAQ,6BAA4B,OAAM,aAAY,GAAE,eAAc,CAAC,EAAC,SAAQ,4BAA2B,OAAM,UAAS,GAAE,EAAC,SAAQ,gBAAe,OAAM,WAAU,QAAO,EAAC,cAAa,EAAC,EAAC,CAAC,GAAE,QAAO,EAAC,SAAQ,mBAAkB,OAAM,WAAU,GAAE,QAAO,GAAE,OAAM,CAAC,EAAC,SAAQ,+EAA8E,OAAM,eAAc,QAAO,EAAC,cAAa,EAAC,EAAC,GAAE,EAAC,SAAQ,MAAK,OAAM,cAAa,CAAC,GAAE,YAAW,EAAC,SAAQ,mCAAkC,OAAM,SAAQ,GAAE,aAAY,EAAC,SAAQ,MAAK,OAAM,UAAS,EAAC;AAAC,EAAE,KAAK,GAAQ,gBAAU,QAAM,EAAC,SAAQ,CAAC,EAAC,SAAQ,mCAAkC,YAAW,MAAG,QAAO,KAAE,GAAE,EAAC,SAAQ,oBAAmB,YAAW,MAAG,QAAO,KAAE,CAAC,GAAE,QAAO,EAAC,SAAQ,kDAAiD,QAAO,KAAE,GAAE,cAAa,EAAC,SAAQ,4FAA2F,YAAW,MAAG,QAAO,EAAC,aAAY,QAAO,EAAC,GAAE,SAAQ,8GAA6G,SAAQ,sBAAqB,UAAS,eAAc,QAAO,6DAA4D,UAAS,gDAA+C,aAAY,gBAAe,GAAQ,gBAAU,aAAiB,gBAAU,OAAO,SAAQ,EAAC,cAAa,CAAO,gBAAU,MAAM,YAAY,GAAE,EAAC,SAAQ,2GAA0G,YAAW,KAAE,CAAC,GAAE,SAAQ,CAAC,EAAC,SAAQ,wBAAuB,YAAW,KAAE,GAAE,EAAC,SAAQ,odAAmd,YAAW,KAAE,CAAC,GAAE,UAAS,qGAAoG,QAAO,EAAC,SAAQ,OAAO,aAAa,SAAO,QAAM,eAAe,SAAO,MAAI,0BAA0B,SAAO,MAAI,4BAA4B,SAAO,MAAI,sCAAsC,SAAO,MAAI,gBAAgB,SAAO,MAAI,oFAAoF,SAAO,MAAI,YAAY,MAAM,GAAE,YAAW,KAAE,GAAE,UAAS,4FAA2F,CAAC,GAAQ,gBAAU,WAAW,YAAY,EAAE,CAAC,EAAE,UAAQ,wEAA6E,gBAAU,aAAa,cAAa,WAAU,EAAC,OAAM,EAAC,SAAQ,OAAO,0DAA0D,SAAO,KAAK,SAAO,QAAM,iEAAiE,SAAO,MAAI,qIAAqI,SAAO,MAAI,kEAAkE,MAAM,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,gBAAe,EAAC,SAAQ,6BAA4B,YAAW,MAAG,OAAM,kBAAiB,QAAa,gBAAU,MAAK,GAAE,mBAAkB,WAAU,eAAc,WAAU,EAAC,GAAE,qBAAoB,EAAC,SAAQ,iMAAgM,OAAM,WAAU,GAAE,WAAU,CAAC,EAAC,SAAQ,uIAAsI,YAAW,MAAG,QAAa,gBAAU,WAAU,GAAE,EAAC,SAAQ,sFAAqF,YAAW,MAAG,QAAa,gBAAU,WAAU,GAAE,EAAC,SAAQ,mEAAkE,YAAW,MAAG,QAAa,gBAAU,WAAU,GAAE,EAAC,SAAQ,+eAA8e,YAAW,MAAG,QAAa,gBAAU,WAAU,CAAC,GAAE,UAAS,4BAA2B,CAAC,GAAQ,gBAAU,aAAa,cAAa,UAAS,EAAC,UAAS,EAAC,SAAQ,SAAQ,QAAO,MAAG,OAAM,UAAS,GAAE,mBAAkB,EAAC,SAAQ,4EAA2E,QAAO,MAAG,QAAO,EAAC,wBAAuB,EAAC,SAAQ,SAAQ,OAAM,SAAQ,GAAE,eAAc,EAAC,SAAQ,oEAAmE,YAAW,MAAG,QAAO,EAAC,6BAA4B,EAAC,SAAQ,aAAY,OAAM,cAAa,GAAE,MAAW,gBAAU,WAAU,EAAC,GAAE,QAAO,UAAS,EAAC,GAAE,mBAAkB,EAAC,SAAQ,6EAA4E,YAAW,MAAG,QAAO,MAAG,OAAM,WAAU,EAAC,CAAC,GAAQ,gBAAU,aAAa,cAAa,YAAW,EAAC,oBAAmB,EAAC,SAAQ,qFAAoF,YAAW,MAAG,OAAM,WAAU,EAAC,CAAC,GAAQ,gBAAU,WAAe,gBAAU,OAAO,IAAI,WAAW,UAAS,YAAY,GAAQ,gBAAU,OAAO,IAAI,aAAa,yNAAyN,QAAO,YAAY,IAAS,gBAAU,KAAS,gBAAU,YAAiB,gBAAU,eAAmB,gBAAU,OAAO,cAAa,EAAC,SAAQ,wUAAuU,UAAS,4DAA2D,CAAC,GAAQ,gBAAU,aAAa,YAAY,EAAE,QAAM,YAAW,OAAa,gBAAU,aAAa,WAAU,OAAa,gBAAU,aAAa,kBAAkB,GAAQ,gBAAU,UAAc,gBAAU,aAAa,gBAAe,UAAS,EAAC,KAAI,EAAC,SAAQ,6EAA4E,YAAW,MAAG,QAAa,gBAAU,OAAM,EAAC,CAAC,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,aAAY,IAAE,EAAC,SAAQ,cAAa,OAAM,WAAU;AAAE,IAAE,UAAU,eAAa,EAAE,UAAU,OAAO,cAAa,EAAC,SAAQ,GAAE,QAAO,CAAC,EAAC,SAAQ,0BAAyB,QAAO,KAAE,GAAE,EAAC,SAAQ,0BAAyB,QAAO,MAAG,QAAO,EAAC,eAAc,EAAC,EAAC,CAAC,GAAE,SAAQ,oSAAmS,gBAAe,EAAC,SAAQ,cAAa,OAAM,WAAU,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,gBAAe,WAAU,EAAC,qBAAoB,EAAC,SAAQ,kBAAiB,OAAM,UAAS,GAAE,eAAc,EAAC,SAAQ,sBAAqB,OAAM,SAAQ,QAAO,EAAC,SAAQ,GAAE,eAAc,EAAC,EAAC,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,gBAAe,UAAS,EAAC,qBAAoB,EAAC,SAAQ,0BAAyB,QAAO,EAAC,WAAU,EAAC,SAAQ,SAAQ,OAAM,cAAa,GAAE,QAAO,EAAC,SAAQ,WAAU,OAAM,uBAAsB,QAAO,EAAE,UAAU,WAAU,EAAC,EAAC,GAAE,oBAAmB,CAAC,EAAC,SAAQ,kBAAiB,QAAO,MAAG,OAAM,SAAQ,GAAE,EAAC,SAAQ,kBAAiB,QAAO,MAAG,OAAM,UAAS,QAAO,EAAC,eAAc,EAAC,EAAC,CAAC,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,gBAAe,WAAU,EAAC,UAAS,yBAAwB,CAAC,GAAE,OAAO,EAAE,UAAU,aAAa,iBAAiB,GAAE,EAAE,UAAU,SAAO,EAAE,UAAU;AAAY,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,UAAU,cAAY,EAAC,WAAU,EAAC,SAAQ,kEAAiE,YAAW,KAAE,GAAE,SAAQ,EAAC,SAAQ,yDAAwD,YAAW,KAAE,GAAE,aAAY,OAAM;AAAE,SAAO,eAAe,GAAE,cAAa,EAAC,OAAM,SAASC,IAAE,GAAE;AAAC,KAACA,KAAE,YAAU,OAAOA,KAAE,CAACA,EAAC,IAAEA,IAAG,QAAQ,SAASA,IAAE;AAAC,UAAI,IAAE,SAASA,IAAE;AAAC,QAAAA,GAAE,WAASA,GAAE,SAAO,CAAC,IAAGA,GAAE,OAAO,OAAK;AAAA,MAAC,GAAE,IAAE;AAAc,UAAG,IAAE,EAAE,UAAUA,EAAC,GAAE;AAAC,YAAI,GAAE,IAAE,EAAE,CAAC;AAAE,aAAI,IAAE,IAAE,KAAG,IAAE,EAAE,UAAU,aAAaA,IAAE,WAAU,EAAC,eAAc,EAAC,SAAQ,yCAAwC,YAAW,MAAG,OAAM,UAAS,EAAC,CAAC,GAAG,CAAC,cAAa,WAAS,IAAE,EAAE,CAAC,IAAE,EAAC,SAAQ,EAAC,IAAG,MAAM,QAAQ,CAAC;AAAE,mBAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAE,GAAE;AAAI,cAAE,CAAC,aAAY,WAAS,EAAE,CAAC,IAAE,EAAC,SAAQ,EAAE,CAAC,EAAC,IAAG,EAAE,EAAE,CAAC,CAAC;AAAA;AAAO,YAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,CAAC,GAAE,EAAE,WAAW,CAAC,QAAO,cAAa,KAAK,GAAE,CAAC;AAAC,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,+EAA8E,KAAG,EAAE,UAAU,MAAI,EAAC,SAAQ,oBAAmB,QAAO,EAAC,SAAQ,OAAO,eAAa,sBAAsB,SAAO,MAAI,EAAE,SAAO,QAAM,kBAAkB,MAAM,GAAE,QAAO,EAAC,MAAK,YAAW,8BAA6B,EAAC,SAAQ,6FAA4F,YAAW,MAAG,OAAM,WAAU,GAAE,SAAQ,EAAC,SAAQ,0CAAyC,YAAW,KAAE,EAAC,EAAC,GAAE,KAAI,EAAC,SAAQ,OAAO,iBAAe,EAAE,SAAO,MAAI,8BAA8B,SAAO,QAAO,GAAG,GAAE,QAAO,MAAG,QAAO,EAAC,UAAS,SAAQ,aAAY,WAAU,QAAO,EAAC,SAAQ,OAAO,MAAI,EAAE,SAAO,GAAG,GAAE,OAAM,MAAK,EAAC,EAAC,GAAE,UAAS,EAAC,SAAQ,OAAO,sDAAqD,EAAE,SAAO,eAAe,GAAE,YAAW,KAAE,GAAE,QAAO,EAAC,SAAQ,GAAE,QAAO,KAAE,GAAE,UAAS,EAAC,SAAQ,qFAAoF,YAAW,KAAE,GAAE,WAAU,iBAAgB,UAAS,EAAC,SAAQ,mCAAkC,YAAW,KAAE,GAAE,aAAY,YAAW,GAAE,EAAE,UAAU,IAAI,OAAO,OAAO,OAAK,EAAE,UAAU,KAAI,EAAE,UAAU;AAAQ,QAAI,EAAE,IAAI,WAAW,SAAQ,KAAK,GAAE,EAAE,IAAI,aAAa,SAAQ,KAAK;AAAE,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,iDAAgD,KAAG,EAAE,UAAU,IAAI,WAAS,EAAC,SAAQ,EAAE,UAAU,IAAI,SAAS,SAAQ,YAAW,MAAG,QAAO,IAAE,EAAC,kBAAiB,gEAA+D,gBAAe,WAAU,OAAM,YAAW,IAAG,WAAU,WAAU,EAAC,SAAQ,OAAO,qBAAoB,EAAE,SAAO,OAAO,GAAE,QAAO,MAAG,QAAO,EAAC,aAAY,WAAU,oBAAmB,EAAC,SAAQ,cAAa,YAAW,MAAG,OAAM,UAAS,GAAE,WAAU,EAAC,SAAQ,6CAA4C,YAAW,MAAG,QAAO,EAAC,aAAY,MAAK,EAAC,GAAE,aAAY,EAAC,SAAQ,qCAAoC,YAAW,KAAE,GAAE,cAAa,CAAC,GAAE,EAAC,SAAQ,6CAA4C,YAAW,KAAE,CAAC,GAAE,UAAS,YAAW,EAAC,GAAE,QAAO,CAAC,EAAC,SAAQ,mDAAkD,YAAW,MAAG,QAAO,EAAC,QAAO,UAAS,UAAS,OAAM,EAAC,GAAE,EAAC,SAAQ,iCAAgC,YAAW,KAAE,CAAC,GAAE,YAAW,eAAc,aAAY,QAAO,EAAC,GAAE,EAAE,UAAU,IAAI,OAAO,OAAO,4BAA4B,EAAE,SAAO,GAAE,EAAE,UAAU,aAAa,OAAM,YAAW,EAAC,UAAS,EAAC,SAAQ,+EAA8E,YAAW,KAAE,EAAC,CAAC,GAAE,EAAC,SAAQ,gCAA+B,YAAW,KAAE,IAAG,IAAE,EAAC,SAAQ,wCAAuC,YAAW,KAAE;AAAE,IAAE,UAAU,aAAa,OAAM,YAAW,EAAC,UAAS,EAAC,SAAQ,sBAAqB,YAAW,KAAE,GAAE,SAAQ,EAAC,SAAQ,sBAAqB,OAAM,QAAO,GAAE,OAAM,CAAC,EAAC,SAAQ,m7CAAk7C,YAAW,KAAE,GAAE,EAAC,SAAQ,6JAA4J,QAAO,EAAC,MAAK,GAAE,QAAO,GAAE,UAAS,gBAAe,aAAY,QAAO,EAAC,CAAC,GAAE,QAAO,mBAAkB,MAAK,GAAE,QAAO,EAAC,CAAC;AAAC,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,oBAAmB,IAAE,oFAAmF,IAAE,QAAM,EAAE,SAAO,aAAY,EAAE,SAAO,QAAM,EAAE,SAAO,aAAY,EAAE,SAAO,OAAM,IAAE,kJAAkJ,OAAO,QAAQ,YAAW,WAAU;AAAC,WAAM,2EAA2E;AAAA,EAAM,CAAC,GAAE,IAAE,8CAA8C;AAAO,WAAS,EAAEA,IAAEC,IAAE;AAAC,IAAAA,MAAGA,MAAG,IAAI,QAAQ,MAAK,EAAE,IAAE;AAAI,QAAIC,KAAE,yFAAyF,OAAO,QAAQ,aAAY,WAAU;AAAC,aAAO;AAAA,IAAC,CAAC,EAAE,QAAQ,cAAa,WAAU;AAAC,aAAOF;AAAA,IAAC,CAAC;AAAE,WAAO,OAAOE,IAAED,EAAC;AAAA,EAAC;AAAC,IAAE,UAAU,OAAK,EAAC,QAAO,EAAC,SAAQ,OAAO,6FAA6F,OAAO,QAAQ,aAAY,WAAU;AAAC,WAAO;AAAA,EAAC,CAAC,CAAC,GAAE,YAAW,MAAG,OAAM,SAAQ,GAAE,SAAQ,OAAM,KAAI,EAAC,SAAQ,OAAO,kEAAkE,OAAO,QAAQ,aAAY,WAAU;AAAC,WAAO;AAAA,EAAC,CAAC,EAAE,QAAQ,YAAW,WAAU;AAAC,WAAM,QAAM,IAAE,MAAI,IAAE;AAAA,EAAG,CAAC,CAAC,GAAE,YAAW,MAAG,QAAO,MAAG,OAAM,SAAQ,GAAE,WAAU,EAAC,SAAQ,iBAAgB,YAAW,MAAG,OAAM,YAAW,GAAE,UAAS,EAAC,SAAQ,EAAE,sJAAsJ,MAAM,GAAE,YAAW,MAAG,OAAM,SAAQ,GAAE,SAAQ,EAAC,SAAQ,EAAE,aAAa,QAAO,GAAG,GAAE,YAAW,MAAG,OAAM,YAAW,GAAE,MAAK,EAAC,SAAQ,EAAE,SAAS,QAAO,GAAG,GAAE,YAAW,MAAG,OAAM,YAAW,GAAE,QAAO,EAAC,SAAQ,EAAE,CAAC,GAAE,YAAW,MAAG,QAAO,KAAE,GAAE,QAAO,EAAC,SAAQ,EAAE,iFAAiF,QAAO,GAAG,GAAE,YAAW,KAAE,GAAE,KAAI,GAAE,WAAU,GAAE,aAAY,4BAA2B,GAAE,EAAE,UAAU,MAAI,EAAE,UAAU;AAAI,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,2CAA2C;AAAO,WAAS,EAAED,IAAE;AAAC,WAAOA,KAAEA,GAAE,QAAQ,YAAW,WAAU;AAAC,aAAO;AAAA,IAAC,CAAC,GAAE,OAAO,0BAA0B,SAAO,QAAMA,KAAE,GAAG;AAAA,EAAC;AAAC,MAAI,IAAE,4DAA4D,QAAO,IAAE,+CAA+C,OAAO,QAAQ,OAAM,WAAU;AAAC,WAAO;AAAA,EAAC,CAAC,GAAE,IAAE,sEAAsE,QAAO,KAAG,EAAE,UAAU,WAAS,EAAE,UAAU,OAAO,UAAS,CAAC,CAAC,GAAE,EAAE,UAAU,aAAa,YAAW,UAAS,EAAC,sBAAqB,EAAC,SAAQ,mDAAkD,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,aAAY,aAAY,gBAAe,EAAC,SAAQ,kBAAiB,OAAM,CAAC,QAAO,eAAe,GAAE,QAAO,EAAE,UAAU,KAAI,EAAC,EAAC,GAAE,YAAW,EAAC,SAAQ,mBAAkB,OAAM,cAAa,GAAE,OAAM,EAAC,SAAQ,OAAO,MAAI,IAAE,IAAE,QAAM,IAAE,MAAK,GAAG,GAAE,QAAO,EAAC,mBAAkB,EAAC,SAAQ,OAAO,OAAK,IAAE,IAAE,SAAO,IAAE,KAAK,GAAE,YAAW,MAAG,QAAO,EAAC,cAAa,EAAC,SAAQ,OAAO,CAAC,GAAE,QAAO,EAAE,UAAU,SAAQ,GAAE,aAAY,KAAI,EAAC,GAAE,cAAa,EAAC,SAAQ,OAAO,OAAK,IAAE,MAAI,IAAE,GAAG,GAAE,YAAW,MAAG,QAAO,EAAC,aAAY,eAAc,EAAC,GAAE,oBAAmB,EAAC,SAAQ,OAAO,MAAI,IAAE,GAAG,GAAE,QAAO,EAAC,gBAAe,EAAC,SAAQ,OAAO,CAAC,GAAE,OAAM,aAAY,QAAO,EAAE,UAAU,SAAQ,GAAE,aAAY,KAAI,EAAC,EAAC,EAAC,GAAE,MAAK,CAAC,EAAC,SAAQ,wFAAuF,YAAW,MAAG,OAAM,UAAS,GAAE,EAAC,SAAQ,sBAAqB,QAAO,MAAG,QAAO,EAAC,cAAa,EAAC,SAAQ,sDAAqD,YAAW,KAAE,GAAE,iBAAgB,EAAC,SAAQ,YAAW,YAAW,KAAE,GAAE,aAAY,MAAK,EAAC,CAAC,GAAE,OAAM,CAAC,EAAC,SAAQ,2CAA0C,OAAM,aAAY,QAAO,EAAC,aAAY,YAAW,EAAC,GAAE,EAAC,SAAQ,cAAa,YAAW,MAAG,OAAM,aAAY,QAAO,EAAC,aAAY,UAAS,EAAC,CAAC,GAAE,IAAG,EAAC,SAAQ,yCAAwC,YAAW,MAAG,OAAM,cAAa,GAAE,MAAK,EAAC,SAAQ,oCAAmC,YAAW,MAAG,OAAM,cAAa,GAAE,iBAAgB,EAAC,SAAQ,sHAAqH,QAAO,EAAC,UAAS,EAAC,SAAQ,iBAAgB,YAAW,KAAE,GAAE,QAAO,gEAA+D,aAAY,iBAAgB,GAAE,OAAM,MAAK,GAAE,MAAK,EAAC,SAAQ,EAAE,kGAAkG,MAAM,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,SAAQ,EAAC,SAAQ,uBAAsB,YAAW,MAAG,QAAO,CAAC,EAAC,GAAE,aAAY,UAAS,EAAC,GAAE,QAAO,EAAC,SAAQ,EAAE,kGAAkG,MAAM,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,SAAQ,EAAC,SAAQ,qBAAoB,YAAW,MAAG,QAAO,CAAC,EAAC,GAAE,aAAY,OAAM,EAAC,GAAE,QAAO,EAAC,SAAQ,EAAE,2BAA2B,MAAM,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,SAAQ,EAAC,SAAQ,wBAAuB,YAAW,MAAG,QAAO,CAAC,EAAC,GAAE,aAAY,MAAK,EAAC,GAAE,gBAAe,EAAC,SAAQ,oEAAmE,YAAW,MAAG,QAAO,MAAG,OAAM,CAAC,QAAO,SAAS,EAAC,GAAE,KAAI,EAAC,SAAQ,EAAE,mGAAmG,MAAM,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,UAAS,MAAK,SAAQ,EAAC,SAAQ,qBAAoB,YAAW,MAAG,QAAO,CAAC,EAAC,GAAE,UAAS,EAAC,SAAQ,8BAA6B,YAAW,KAAE,GAAE,KAAI,EAAC,SAAQ,kBAAiB,YAAW,KAAE,GAAE,QAAO,EAAC,SAAQ,qCAAoC,YAAW,KAAE,EAAC,EAAC,EAAC,CAAC,GAAE,CAAC,OAAM,QAAO,UAAS,QAAQ,EAAE,QAAQ,SAASC,IAAE;AAAC,KAAC,OAAM,QAAO,UAAS,UAAS,cAAc,EAAE,QAAQ,SAASD,IAAE;AAAC,MAAAC,OAAID,OAAI,EAAE,UAAU,SAASC,EAAC,EAAE,OAAO,QAAQ,OAAOD,EAAC,IAAE,EAAE,UAAU,SAASA,EAAC;AAAA,IAAE,CAAC;AAAA,EAAC,CAAC,GAAE,EAAE,MAAM,IAAI,kBAAiB,SAASA,IAAE;AAAC,mBAAaA,GAAE,YAAU,SAAOA,GAAE,YAAU,CAAC,SAASA,GAAEC,IAAE;AAAC,UAAGA,MAAG,YAAU,OAAOA;AAAE,iBAAQC,KAAE,GAAEC,KAAEF,GAAE,QAAOC,KAAEC,IAAED,MAAI;AAAC,cAAIE,IAAE,IAAEH,GAAEC,EAAC;AAAE,qBAAS,EAAE,OAAKF,GAAE,EAAE,OAAO,KAAGI,KAAE,EAAE,QAAQ,CAAC,GAAE,IAAE,EAAE,QAAQ,CAAC,GAAEA,MAAG,KAAG,oBAAkBA,GAAE,QAAM,iBAAe,EAAE,QAAM,YAAU,OAAOA,GAAE,YAAUA,KAAEA,GAAE,QAAQ,QAAQ,QAAO,OAAO,EAAE,QAAQ,WAAU,IAAI,GAAEA,KAAE,eAAaA,MAAG,eAAe,KAAKA,EAAC,KAAG,CAAC,EAAE,GAAG,CAAC,EAAE,YAAY,IAAG,EAAE,QAAM,YAAU,OAAO,EAAE,QAAM,EAAE,QAAM,CAAC,EAAE,OAAMA,EAAC,IAAE,EAAE,MAAM,KAAKA,EAAC,IAAE,EAAE,QAAM,CAACA,EAAC;AAAA,QAAG;AAAA,IAAC,EAAEJ,GAAE,MAAM;AAAA,EAAC,CAAC,GAAE,EAAE,MAAM,IAAI,QAAO,SAASA,IAAE;AAAC,QAAG,iBAAeA,GAAE,MAAK;AAAC,eAAQC,KAAE,IAAGC,KAAE,GAAEC,KAAEH,GAAE,QAAQ,QAAOE,KAAEC,IAAED,MAAI;AAAC,YAAIE,KAAEJ,GAAE,QAAQE,EAAC,GAAEE,KAAE,gBAAgB,KAAKA,EAAC;AAAE,YAAGA,IAAE;AAAC,UAAAH,KAAEG,GAAE,CAAC;AAAE;AAAA,QAAK;AAAA,MAAC;AAAC,UAAI,GAAE,IAAE,EAAE,UAAUH,EAAC;AAAE,UAAED,GAAE,UAAQ,EAAE,UAAU,SAASA,IAAE;AAAC,QAAAA,KAAEA,GAAE,QAAQ,GAAE,EAAE;AAAE,eAAOA,KAAEA,GAAE,QAAQ,iCAAgC,SAASA,IAAEC,IAAE;AAAC,cAAIC;AAAE,iBAAM,SAAOD,KAAEA,GAAE,YAAY,GAAG,CAAC,KAAGC,KAAE,QAAMD,GAAE,CAAC,IAAE,SAASA,GAAE,MAAM,CAAC,GAAE,EAAE,IAAE,OAAOA,GAAE,MAAM,CAAC,CAAC,GAAE,EAAEC,EAAC,KAAG,EAAED,EAAC,KAAGD;AAAA,QAAC,CAAC;AAAA,MAAC,EAAEA,GAAE,OAAO,GAAE,GAAEC,EAAC,IAAEA,MAAG,WAASA,MAAG,EAAE,QAAQ,eAAa,IAAE,SAAO,oBAAI,QAAM,QAAQ,IAAE,MAAI,KAAK,MAAM,OAAK,KAAK,OAAO,CAAC,GAAED,GAAE,WAAW,KAAG,GAAE,EAAE,QAAQ,WAAW,cAAcC,IAAE,WAAU;AAAC,YAAID,KAAE,SAAS,eAAe,CAAC;AAAE,QAAAA,OAAIA,GAAE,YAAU,EAAE,UAAUA,GAAE,aAAY,EAAE,UAAUC,EAAC,GAAEA,EAAC;AAAA,MAAE,CAAC;AAAA,IAAE;AAAA,EAAC,CAAC,GAAE,OAAO,EAAE,UAAU,OAAO,IAAI,QAAQ,QAAO,IAAI,IAAG,IAAE,EAAC,KAAI,KAAI,IAAG,KAAI,IAAG,KAAI,MAAK,IAAG,GAAE,IAAE,OAAO,iBAAe,OAAO;AAAa,IAAE,UAAU,KAAG,EAAE,UAAU;AAAQ,EAAE,KAAK,GAAQ,gBAAU,UAAQ,EAAC,SAAQ,OAAM,aAAY,EAAC,SAAQ,oEAAmE,QAAO,MAAG,OAAM,UAAS,QAAO,EAAC,qBAAoB,EAAC,SAAQ,mCAAkC,YAAW,MAAG,QAAa,gBAAU,SAAQ,EAAC,EAAC,GAAE,QAAO,EAAC,SAAQ,kDAAiD,QAAO,KAAE,GAAE,QAAO,4CAA2C,SAAQ,sBAAqB,UAAS,gBAAe,WAAU,EAAC,SAAQ,eAAc,OAAM,WAAU,GAAE,aAAY,EAAC,SAAQ,kEAAiE,QAAO,KAAE,GAAE,cAAa,EAAC,SAAQ,qBAAoB,OAAM,aAAY,GAAE,QAAO,uCAAsC,UAAS,sBAAqB,cAAa,EAAC,SAAQ,mFAAkF,YAAW,KAAE,GAAE,UAAS,EAAC,SAAQ,gDAA+C,YAAW,MAAG,OAAM,WAAU,GAAE,uBAAsB,EAAC,SAAQ,+BAA8B,YAAW,MAAG,OAAM,WAAU,GAAE,oBAAmB,EAAC,SAAQ,4BAA2B,YAAW,MAAG,OAAM,WAAU,GAAE,SAAQ,wIAAuI,UAAS,gBAAe,kBAAiB,gBAAe,QAAO,gBAAe,aAAY,kBAAiB,UAAS,MAAK,GAAQ,YAAM,IAAI,kBAAiB,SAAS,GAAE;AAAC,MAAG,cAAY,EAAE;AAAS,aAAQ,IAAE,EAAE,OAAO,OAAO,SAASD,IAAE;AAAC,aAAM,YAAU,OAAOA,MAAG,cAAYA,GAAE,QAAM,aAAWA,GAAE;AAAA,IAAI,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,UAAQ;AAAC,UAAI,IAAE,EAAE,GAAG;AAAE,UAAG,cAAY,EAAE,QAAM,eAAa,EAAE,SAAQ;AAAC,YAAI,IAAE,CAAC;AAAE,YAAG,EAAE,CAAC,uBAAsB,aAAa,CAAC,KAAG,QAAM,EAAE,CAAC,EAAE,SAAQ;AAAC,eAAG;AAAE,cAAI,IAAE,EAAE,QAAO,MAAM;AAAE,cAAG,OAAK;AAAE;AAAS,iBAAK,IAAE,GAAE,KAAI;AAAC,gBAAI,IAAE,EAAE,CAAC;AAAE,2BAAa,EAAE,SAAO,EAAE,GAAE,gBAAgB,GAAE,EAAE,KAAK,EAAE,OAAO;AAAA,UAAE;AAAC,cAAE,IAAE;AAAA,QAAC;AAAC,YAAG,EAAE,CAAC,eAAc,gBAAgB,CAAC,KAAG,QAAM,EAAE,CAAC,EAAE,YAAU,KAAI,EAAE,EAAE,CAAC,GAAE,mBAAmB,GAAE,IAAE,EAAE,SAAQ;AAAC,cAAI,IAAE,EAAE,QAAO,MAAM;AAAE,cAAG,OAAK;AAAE,qBAAQ,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,kBAAI,IAAE,EAAE,CAAC;AAAE,6BAAa,EAAE,QAAM,KAAG,EAAE,QAAQ,EAAE,OAAO,KAAG,EAAE,GAAE,gBAAgB;AAAA,YAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,WAAO,EAAE,IAAEA,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAEC,IAAE;AAAC,IAAAA,KAAEA,MAAG;AAAE,aAAQC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,UAAIC,KAAE,EAAED,KAAED,EAAC;AAAE,UAAG,CAACE,MAAGA,GAAE,SAAOH,GAAEE,EAAC;AAAE;AAAA,IAAM;AAAC,WAAO;AAAA,EAAC;AAAC,WAAS,EAAEF,IAAEC,IAAE;AAAC,aAAQC,KAAE,GAAEC,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAIC,KAAE,EAAED,EAAC,GAAEE,KAAED,GAAE;AAAQ,UAAG,kBAAgBA,GAAE,QAAM,YAAU,OAAOC;AAAE,YAAGL,GAAE,KAAKK,EAAC;AAAE,UAAAH;AAAA,iBAAYD,GAAE,KAAKI,EAAC,KAAG,MAAI,EAAEH;AAAE,iBAAOC;AAAA;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAC,WAAS,EAAEH,IAAEC,IAAE;AAAC,QAAIC,KAAEF,GAAE;AAAM,IAAAE,KAAE,MAAM,QAAQA,EAAC,MAAIF,GAAE,QAAME,KAAE,CAACA,EAAC,KAAGF,GAAE,QAAME,KAAE,CAAC,GAAEA,GAAE,KAAKD,EAAC;AAAA,EAAC;AAAC,CAAC,GAAQ,gBAAU,MAAI,EAAC,SAAQ,EAAC,SAAQ,iDAAgD,YAAW,KAAE,GAAE,UAAS,CAAC,EAAC,SAAQ,uCAAsC,QAAO,KAAE,GAAE,UAAU,GAAE,QAAO,EAAC,SAAQ,mDAAkD,QAAO,MAAG,YAAW,KAAE,GAAE,YAAW,EAAC,SAAQ,uCAAsC,QAAO,MAAG,YAAW,MAAG,QAAO,EAAC,aAAY,QAAO,EAAC,GAAE,UAAS,6FAA4F,SAAQ,w9EAAu9E,SAAQ,4BAA2B,QAAO,6CAA4C,UAAS,gIAA+H,aAAY,cAAa,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,UAAU,WAAW,iBAAiB,GAAE,IAAE,EAAE,QAAQ,QAAO,IAAE,EAAE,OAAO,eAAc,IAAE,EAAE,OAAO,2BAA2B,GAAE,IAAE,EAAE,QAAQ;AAAO,WAAS,EAAED,IAAEC,IAAE;AAAC,QAAG,EAAE,UAAUD,EAAC;AAAE,aAAM,EAAC,SAAQ,OAAO,SAAOC,KAAE,WAAS,CAAC,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,wBAAuB,EAAC,SAAQ,SAAQ,OAAM,SAAQ,GAAE,iBAAgB,EAAC,SAAQ,WAAU,OAAMD,GAAC,EAAC,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,IAAAF,KAAE,EAAC,MAAKA,IAAE,SAAQC,IAAE,UAASC,GAAC;AAAE,WAAO,EAAE,MAAM,IAAI,mBAAkBF,EAAC,GAAEA,GAAE,SAAO,EAAE,SAASA,GAAE,MAAKA,GAAE,OAAO,GAAE,EAAE,MAAM,IAAI,kBAAiBA,EAAC,GAAEA,GAAE;AAAA,EAAM;AAAC,WAAS,EAAEG,IAAEH,IAAE,GAAE;AAAC,QAAIC,KAAE,EAAE,SAASE,IAAE,EAAC,eAAc,EAAC,SAAQ,OAAO,CAAC,GAAE,YAAW,KAAE,EAAC,CAAC,GAAE,IAAE,GAAE,IAAE,CAAC,GAAEF,KAAE,EAAEA,GAAE,IAAI,SAASD,IAAE;AAAC,UAAG,YAAU,OAAOA;AAAE,eAAOA;AAAE,eAAQC,IAAEC,IAAEF,KAAEA,GAAE,SAAQ,OAAKG,GAAE,SAASD,KAAE,KAAID,KAAE,QAAM,EAAE,YAAY,IAAE,MAAIC,KAAE,MAAM;AAAG;AAAC,aAAO,EAAED,EAAC,IAAED,IAAEC;AAAA,IAAC,CAAC,EAAE,KAAK,EAAE,GAAED,IAAE,CAAC,GAAE,IAAE,OAAO,KAAK,CAAC;AAAE,WAAO,IAAE,GAAE,SAASA,GAAEC,IAAE;AAAC,eAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,YAAG,KAAG,EAAE;AAAO;AAAO,YAAIC,IAAEC,IAAEC,IAAE,GAAE,GAAEC,IAAEC,IAAE,IAAEN,GAAEC,EAAC;AAAE,oBAAU,OAAO,KAAG,YAAU,OAAO,EAAE,WAASC,KAAE,EAAE,CAAC,GAAE,QAAMI,MAAGD,KAAE,YAAU,OAAO,IAAE,IAAE,EAAE,SAAS,QAAQH,EAAC,OAAK,EAAE,GAAEC,KAAEE,GAAE,UAAU,GAAEC,EAAC,GAAE,IAAE,EAAEJ,EAAC,GAAEE,KAAE,SAAQ,IAAE,CAAC,GAAG,2BAA2B,IAAE,GAAE,OAAK,IAAE,EAAE,SAAS,GAAE,CAAC,GAAG,YAAUA,KAAE,CAAC,GAAE,CAAC,GAAG,KAAK,MAAMA,IAAE,EAAE,EAAE,CAAC,GAAE,EAAE,UAAU,YAAW,YAAY,CAAC,GAAE,EAAE,OAAO,MAAM,GAAEA,EAAC,IAAGA,KAAE,IAAI,EAAE,MAAM,iBAAgB,GAAE,EAAE,OAAM,CAAC,GAAE,IAAEC,GAAE,UAAUC,KAAEJ,GAAE,MAAM,GAAE,IAAE,CAAC,GAAEC,MAAG,EAAE,KAAKA,EAAC,GAAE,EAAE,KAAKC,EAAC,GAAE,MAAIL,GAAEM,KAAE,CAAC,CAAC,CAAC,GAAE,EAAE,KAAK,MAAM,GAAEA,EAAC,IAAG,YAAU,OAAO,KAAGL,GAAE,OAAO,MAAMA,IAAE,CAACC,IAAE,CAAC,EAAE,OAAO,CAAC,CAAC,GAAEA,MAAG,EAAE,SAAO,KAAG,EAAE,UAAQ,OAAKK,KAAE,EAAE,SAAQ,MAAM,QAAQA,EAAC,IAAEP,GAAEO,EAAC,IAAEP,GAAE,CAACO,EAAC,CAAC;AAAA,MAAE;AAAA,IAAC,EAAEN,EAAC,GAAE,IAAI,EAAE,MAAM,GAAEA,IAAE,cAAY,GAAEE,EAAC;AAAA,EAAC;AAAC,IAAE,UAAU,WAAW,iBAAiB,IAAE,CAAC,EAAE,OAAM,0HAA0H,MAAM,GAAE,EAAE,QAAO,yCAAyC,MAAM,GAAE,EAAE,OAAM,QAAQ,MAAM,GAAE,EAAE,YAAW,oBAAoB,MAAM,GAAE,EAAE,WAAU,6CAA6C,MAAM,GAAE,EAAE,OAAM,QAAQ,MAAM,GAAE,CAAC,EAAE,OAAO,OAAO;AAAE,MAAI,IAAE,EAAC,YAAW,MAAG,IAAG,MAAG,YAAW,MAAG,IAAG,MAAG,KAAI,MAAG,KAAI,KAAE;AAAE,WAAS,EAAEH,IAAE;AAAC,WAAM,YAAU,OAAOA,KAAEA,KAAE,MAAM,QAAQA,EAAC,IAAEA,GAAE,IAAI,CAAC,EAAE,KAAK,EAAE,IAAE,EAAEA,GAAE,OAAO;AAAA,EAAC;AAAC,IAAE,MAAM,IAAI,kBAAiB,SAASA,IAAE;AAAC,IAAAA,GAAE,YAAY,KAAG,CAAC,SAASA,GAAEC,IAAE;AAAC,eAAQC,KAAE,GAAEC,KAAEF,GAAE,QAAOC,KAAEC,IAAED,MAAI;AAAC,YAAI,GAAEG,IAAE,GAAE,IAAEJ,GAAEC,EAAC;AAAE,oBAAU,OAAO,MAAI,IAAE,EAAE,SAAQ,MAAM,QAAQ,CAAC,IAAE,sBAAoB,EAAE,QAAM,IAAE,EAAE,CAAC,GAAE,MAAI,EAAE,UAAQ,YAAU,OAAO,KAAG,oBAAkB,EAAE,SAAOG,KAAE,EAAE,CAAC,GAAE,IAAE,EAAE,OAAM,IAAE,MAAM,QAAQ,CAAC,IAAE,EAAE,CAAC,IAAE,GAAE,IAAE,EAAE,UAAU,CAAC,OAAK,EAAE,CAAC,IAAE,EAAEA,IAAE,GAAE,CAAC,MAAIL,GAAE,CAAC,IAAE,YAAU,OAAO,KAAGA,GAAE,CAAC,CAAC,CAAC;AAAA,MAAE;AAAA,IAAC,EAAEA,GAAE,MAAM;AAAA,EAAC,CAAC;AAAC,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,IAAE,UAAU,aAAW,EAAE,UAAU,OAAO,cAAa,EAAC,cAAa,EAAC,SAAQ,gLAA+K,YAAW,MAAG,QAAO,MAAG,QAAO,KAAI,GAAE,SAAQ,wFAAuF,CAAC,GAAE,EAAE,UAAU,WAAW,QAAQ,KAAK,sDAAqD,4FAA2F,4BAA4B,GAAE,OAAO,EAAE,UAAU,WAAW,WAAU,OAAO,EAAE,UAAU,WAAW,kBAAkB;AAAE,MAAI,IAAE,EAAE,UAAU,OAAO,cAAa,CAAC,CAAC;AAAE,SAAO,EAAE,YAAY,GAAE,EAAE,UAAU,WAAW,YAAY,EAAE,SAAO,GAAE,EAAE,UAAU,aAAa,cAAa,YAAW,EAAC,WAAU,EAAC,SAAQ,sBAAqB,QAAO,EAAC,IAAG,EAAC,SAAQ,MAAK,OAAM,WAAU,GAAE,UAAS,WAAU,EAAC,GAAE,oBAAmB,EAAC,SAAQ,0GAAyG,QAAO,MAAG,QAAO,EAAC,UAAS,6DAA4D,SAAQ,EAAC,SAAQ,YAAW,OAAM,cAAa,QAAO,EAAC,EAAC,EAAC,EAAC,CAAC,GAAE,EAAE,UAAU,KAAG,EAAE,UAAU;AAAU,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,UAAU,YAAW,IAAE,2CAA2C,QAAO,IAAE,6CAA2C,IAAE;AAAU,IAAE,UAAU,QAAM,EAAE,UAAU,OAAO,eAAc,EAAC,WAAU,EAAC,SAAQ,OAAO,IAAE,uCAAuC,MAAM,GAAE,YAAW,MAAG,QAAO,EAAC,aAAY,KAAI,EAAC,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,SAAQ,WAAU,EAAC,sBAAqB,EAAC,SAAQ,OAAO,IAAE,wDAAwD,MAAM,GAAE,YAAW,MAAG,QAAO,EAAC,WAAU,EAAC,SAAQ,4BAA2B,YAAW,MAAG,QAAO,EAAC,aAAY,KAAI,EAAC,GAAE,MAAK,EAAC,SAAQ,qBAAoB,YAAW,MAAG,QAAO,GAAE,OAAM,sBAAqB,GAAE,aAAY,SAAQ,EAAC,GAAE,cAAa,CAAC,EAAC,SAAQ,OAAO,mHAAmH,OAAO,QAAQ,WAAU,WAAU;AAAC,WAAO;AAAA,EAAC,CAAC,CAAC,GAAE,YAAW,MAAG,QAAO,EAAC,aAAY,KAAI,EAAC,GAAE,EAAC,SAAQ,OAAO,kBAAgB,CAAC,GAAE,YAAW,MAAG,QAAO,EAAC,QAAO,EAAE,QAAO,QAAO,EAAE,QAAO,SAAQ,EAAE,SAAQ,SAAQ,EAAE,UAAU,WAAW,SAAQ,UAAS,qBAAoB,aAAY,kBAAiB,EAAC,CAAC,GAAE,SAAQ,EAAC,SAAQ,0EAAyE,YAAW,MAAG,QAAO,EAAC,MAAK,EAAC,SAAQ,6BAA4B,YAAW,MAAG,QAAO,GAAE,OAAM,sBAAqB,EAAC,EAAC,EAAC,CAAC,GAAE,EAAE,UAAU,YAAY,WAAW,cAAa,EAAE,UAAU,KAAK;AAAC,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,IAAE,UAAU,OAAK,EAAE,UAAU,OAAO,cAAa,CAAC,CAAC,GAAE,EAAE,UAAU,aAAa,QAAO,WAAU,EAAC,MAAK,CAAC,EAAC,SAAQ,iFAAgF,OAAM,aAAY,CAAC,EAAC,CAAC,GAAE,EAAE,UAAU,KAAK,mBAAmB,EAAE,UAAQ,wKAAuK,OAAO,EAAE,UAAU,KAAK,WAAU,EAAE,UAAU,aAAa,QAAO,YAAW,EAAC,oBAAmB,EAAC,SAAQ,aAAY,OAAM,cAAa,EAAC,CAAC,GAAE,MAAM,QAAQ,EAAE,UAAU,KAAK,OAAO,MAAI,EAAE,UAAU,KAAK,UAAQ,CAAC,EAAE,UAAU,KAAK,OAAO,IAAG,EAAE,UAAU,KAAK,QAAQ,QAAQ,EAAC,SAAQ,mDAAkD,YAAW,KAAE,GAAE,EAAC,SAAQ,yGAAwG,YAAW,KAAE,CAAC;AAAC,EAAE,KAAK,GAAQ,gBAAU,OAAW,gBAAU,OAAO,cAAa,EAAC,SAAQ,kXAAiX,CAAC,GAAQ,gBAAU,aAAa,QAAO,YAAW,EAAC,YAAW,EAAC,SAAQ,SAAQ,OAAM,WAAU,EAAC,CAAC,GAAQ,gBAAU,QAAY,gBAAU,MAAK,SAAS,GAAE;AAAC,WAAS,EAAEA,IAAEC,IAAE;AAAC,WAAO,OAAOD,GAAE,QAAQ,SAAQ,WAAU;AAAC,aAAM,yDAAyD;AAAA,IAAM,CAAC,GAAEC,EAAC;AAAA,EAAC;AAAC,IAAE,UAAU,aAAa,cAAa,qBAAoB,EAAC,mBAAkB,EAAC,SAAQ,OAAO,cAAY,EAAE,UAAU,WAAW,mBAAmB,EAAE,QAAQ,MAAM,GAAE,YAAW,MAAG,OAAM,CAAC,qBAAoB,UAAS,YAAW,iBAAiB,EAAC,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,cAAa,YAAW,EAAC,QAAO,EAAC,SAAQ,OAAO,cAAY,EAAE,UAAU,WAAW,SAAS,MAAM,GAAE,YAAW,MAAG,OAAM,CAAC,YAAW,iBAAiB,EAAC,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,cAAa,YAAW,EAAC,oBAAmB,CAAC,EAAC,SAAQ,0OAAyO,OAAM,aAAY,GAAE,EAAC,SAAQ,yBAAwB,OAAM,aAAY,CAAC,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,cAAa,WAAU,EAAC,SAAQ,EAAC,SAAQ,EAAE,4GAA4G,MAAM,GAAE,YAAW,MAAG,QAAO,EAAE,UAAU,WAAU,GAAE,SAAQ,EAAC,SAAQ,EAAE,mEAAmE,MAAM,GAAE,YAAW,MAAG,QAAO,EAAE,UAAU,WAAU,EAAC,CAAC,GAAE,EAAE,UAAU,WAAW,QAAQ,QAAQ,EAAC,SAAQ,yCAAwC,OAAM,SAAQ,GAAE,EAAC,SAAQ,iGAAgG,OAAM,eAAc,GAAE,EAAC,SAAQ,YAAW,OAAM,CAAC,QAAO,KAAK,EAAC,GAAE,EAAC,SAAQ,iBAAgB,OAAM,MAAK,CAAC,GAAE,EAAE,UAAU,aAAa,cAAa,YAAW,EAAC,QAAO,EAAC,SAAQ,SAAQ,OAAM,WAAU,GAAE,OAAM,EAAC,SAAQ,MAAK,OAAM,WAAU,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,cAAa,eAAc,EAAC,mBAAkB,EAAC,SAAQ,EAAE,gBAAgB,MAAM,GAAE,YAAW,KAAE,GAAE,oBAAmB,EAAC,SAAQ,+CAA8C,YAAW,KAAE,GAAE,KAAI,EAAC,SAAQ,mFAAkF,OAAM,WAAU,GAAE,SAAQ,EAAC,SAAQ,sBAAqB,OAAM,aAAY,EAAC,CAAC;AAAE,WAAQ,IAAE,CAAC,YAAW,qBAAoB,UAAS,mBAAkB,iBAAiB,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,QAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,UAAU,WAAW,CAAC,GAAE,KAAG,IAAE,aAAW,EAAE,KAAK,KAAK,CAAC,IAAE,EAAE,UAAU,WAAW,CAAC,IAAE,EAAC,SAAQ,EAAC,IAAE,GAAG,UAAQ,CAAC;AAAE,KAAC,EAAE,SAAO,GAAG,kBAAkB,IAAE;AAAA,EAAe;AAAC,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,KAAK,MAAM,EAAE,UAAU,UAAU,GAAE,IAAE,+CAA+C,QAAO,IAAE,+CAA+C,QAAO,IAAE,uCAAuC;AAAO,WAAS,EAAED,IAAEC,IAAE;AAAC,WAAOD,KAAEA,GAAE,QAAQ,QAAO,WAAU;AAAC,aAAO;AAAA,IAAC,CAAC,EAAE,QAAQ,aAAY,WAAU;AAAC,aAAO;AAAA,IAAC,CAAC,EAAE,QAAQ,aAAY,WAAU;AAAC,aAAO;AAAA,IAAC,CAAC,GAAE,OAAOA,IAAEC,EAAC;AAAA,EAAC;AAAC,MAAE,EAAE,CAAC,EAAE,QAAO,EAAE,UAAU,MAAI,EAAE,UAAU,OAAO,UAAS,CAAC,GAAE,EAAE,UAAU,IAAI,IAAI,UAAQ,EAAE,wIAAwI,MAAM,GAAE,EAAE,UAAU,IAAI,IAAI,OAAO,IAAI,UAAQ,kBAAiB,EAAE,UAAU,IAAI,IAAI,OAAO,YAAY,EAAE,UAAQ,sEAAqE,EAAE,UAAU,IAAI,IAAI,OAAO,IAAI,OAAO,YAAY,IAAE,6BAA4B,EAAE,UAAU,IAAI,IAAI,OAAO,UAAQ,EAAE,SAAQ,EAAE,UAAU,aAAa,UAAS,aAAY,EAAC,QAAO,EAAC,SAAQ,EAAE,WAAW,MAAM,GAAE,QAAO,EAAE,UAAU,IAAG,EAAC,GAAE,EAAE,UAAU,IAAI,GAAG,GAAE,EAAE,UAAU,aAAa,UAAS,gBAAe,EAAC,QAAO,EAAC,SAAQ,EAAE,YAAY,MAAM,GAAE,OAAM,uBAAsB,QAAO,EAAC,sBAAqB,EAAC,SAAQ,YAAW,OAAM,cAAa,GAAE,MAAK,EAAE,UAAU,IAAG,EAAC,EAAC,GAAE,EAAE,UAAU,IAAI,GAAG;AAAE,WAAS,EAAED,IAAE;AAAC,aAAQC,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,UAAIC,KAAEH,GAAEE,EAAC,GAAEE,KAAE;AAAG,kBAAU,OAAOD,OAAI,UAAQA,GAAE,QAAMA,GAAE,QAAQ,CAAC,KAAG,UAAQA,GAAE,QAAQ,CAAC,EAAE,OAAK,SAAOA,GAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAQ,IAAEF,GAAE,UAAQA,GAAEA,GAAE,SAAO,CAAC,EAAE,YAAU,EAAEE,GAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAGF,GAAE,IAAI,IAAE,SAAOE,GAAE,QAAQA,GAAE,QAAQ,SAAO,CAAC,EAAE,WAASF,GAAE,KAAK,EAAC,SAAQ,EAAEE,GAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAE,cAAa,EAAC,CAAC,IAAE,IAAEF,GAAE,UAAQ,kBAAgBE,GAAE,QAAM,QAAMA,GAAE,UAAQF,GAAEA,GAAE,SAAO,CAAC,EAAE,iBAAe,IAAEA,GAAE,UAAQ,IAAEA,GAAEA,GAAE,SAAO,CAAC,EAAE,gBAAc,kBAAgBE,GAAE,QAAM,QAAMA,GAAE,UAAQF,GAAEA,GAAE,SAAO,CAAC,EAAE,iBAAeG,KAAE,QAAKA,MAAG,YAAU,OAAOD,OAAI,IAAEF,GAAE,UAAQ,MAAIA,GAAEA,GAAE,SAAO,CAAC,EAAE,iBAAeG,KAAE,EAAED,EAAC,GAAED,KAAEF,GAAE,SAAO,MAAI,YAAU,OAAOA,GAAEE,KAAE,CAAC,KAAG,iBAAeF,GAAEE,KAAE,CAAC,EAAE,UAAQE,MAAG,EAAEJ,GAAEE,KAAE,CAAC,CAAC,GAAEF,GAAE,OAAOE,KAAE,GAAE,CAAC,IAAG,IAAEA,OAAI,YAAU,OAAOF,GAAEE,KAAE,CAAC,KAAG,iBAAeF,GAAEE,KAAE,CAAC,EAAE,UAAQE,KAAE,EAAEJ,GAAEE,KAAE,CAAC,CAAC,IAAEE,IAAEJ,GAAE,OAAOE,KAAE,GAAE,CAAC,GAAEA,OAAKF,GAAEE,EAAC,IAAE,IAAI,EAAE,MAAM,cAAaE,IAAE,MAAKA,EAAC,IAAGD,GAAE,WAAS,YAAU,OAAOA,GAAE,WAAS,EAAEA,GAAE,OAAO;AAAA,IAAC;AAAA,EAAC;AAAC,MAAI,IAAE,SAASH,IAAE;AAAC,WAAOA,KAAE,YAAU,OAAOA,KAAEA,KAAE,YAAU,OAAOA,GAAE,UAAQA,GAAE,UAAQA,GAAE,QAAQ,IAAI,CAAC,EAAE,KAAK,EAAE,IAAE;AAAA,EAAE;AAAE,IAAE,MAAM,IAAI,kBAAiB,SAASA,IAAE;AAAC,cAAQA,GAAE,YAAU,UAAQA,GAAE,YAAU,EAAEA,GAAE,MAAM;AAAA,EAAC,CAAC;AAAC,EAAE,KAAK,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,KAAK,MAAM,EAAE,UAAU,UAAU,GAAE,KAAG,EAAE,UAAU,MAAI,EAAE,UAAU,OAAO,OAAM,CAAC,GAAE,OAAO,EAAE,UAAU,IAAI,WAAU,OAAO,EAAE,UAAU,IAAI,kBAAkB,GAAE,EAAE,UAAU,IAAI;AAAK,IAAE,UAAQ,OAAO,qBAAqB,SAAO,QAAM,EAAE,QAAQ,SAAO,KAAI,EAAE,QAAQ,KAAK,GAAE,EAAE,aAAW;AAAE,EAAE,KAAK,GAAQ,gBAAU,QAAM,EAAC,SAAQ,EAAC,SAAQ,wFAAuF,YAAW,MAAG,QAAO,KAAE,GAAE,kBAAiB,CAAC,EAAC,SAAQ,OAAO,YAAY,SAAO,QAAM,8DAA8D,SAAO,MAAI,iEAAiE,SAAO,MAAI,WAAW,MAAM,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,eAAc,EAAC,SAAQ,qCAAoC,YAAW,MAAG,QAAO,KAAI,GAAE,6BAA4B,EAAC,SAAQ,aAAY,OAAM,cAAa,GAAE,aAAY,gBAAe,QAAO,UAAS,EAAC,GAAE,EAAC,SAAQ,OAAO,gBAAgB,SAAO,QAAM,gEAAgE,SAAO,MAAI,2DAA2D,SAAO,MAAM,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,eAAc,EAAC,SAAQ,uCAAsC,YAAW,MAAG,QAAO,KAAI,GAAE,6BAA4B,EAAC,SAAQ,eAAc,OAAM,cAAa,GAAE,QAAO,UAAS,EAAC,CAAC,GAAE,WAAU,EAAC,SAAQ,OAAO,IAAI,SAAO,QAAM,kBAAkB,SAAO,aAAY,8GAA8G,SAAO,QAAM,mBAAmB,SAAO,GAAG,GAAE,OAAM,YAAW,QAAO,EAAC,kBAAiB,SAAQ,SAAQ,sBAAqB,QAAO,qBAAoB,UAAS,oBAAmB,aAAY,QAAO,EAAC,GAAE,SAAQ,EAAC,SAAQ,4FAA2F,OAAM,WAAU,GAAE,mBAAkB,EAAC,SAAQ,UAAS,OAAM,WAAU,GAAE,WAAU,EAAC,SAAQ,QAAO,OAAM,SAAQ,GAAE,uBAAsB,EAAC,SAAQ,kBAAiB,YAAW,MAAG,OAAM,WAAU,GAAE,OAAM,EAAC,SAAQ,4EAA2E,YAAW,MAAG,OAAM,YAAW,GAAE,SAAQ,wnBAAunB,SAAQ,sBAAqB,KAAI,EAAC,SAAQ,WAAU,OAAM,WAAU,GAAE,kBAAiB,WAAU,MAAK,EAAC,SAAQ,SAAQ,OAAM,UAAS,GAAE,QAAO,mFAAkF,cAAa,mCAAkC,UAAS,yBAAwB,UAAS,uCAAsC,UAAS,2CAA0C,aAAY,kBAAiB,GAAQ,gBAAU,MAAM,gBAAgB,EAAE,QAAQ,SAAS,GAAE;AAAC,IAAE,OAAO,cAAc,SAAa,gBAAU;AAAK,CAAC,GAAE,SAAS,GAAE;AAAC,IAAE,UAAU,SAAO,EAAE,UAAU,OAAO,SAAQ,EAAC,SAAQ,EAAC,SAAQ,4bAA2b,YAAW,KAAE,GAAE,UAAS,CAAC,EAAC,SAAQ,kCAAiC,QAAO,KAAE,GAAE,EAAC,SAAQ,oCAAmC,YAAW,MAAG,QAAO,KAAE,CAAC,GAAE,QAAO,mIAAkI,UAAS,uGAAsG,CAAC,GAAE,OAAO,EAAE,UAAU,OAAO,YAAY;AAAE,MAAI,IAAE,EAAC,6BAA4B,EAAC,SAAQ,cAAa,OAAM,cAAa,GAAE,YAAW,EAAC,SAAQ,WAAU,QAAO,EAAE,UAAU,OAAM,EAAC;AAAE,IAAE,UAAU,aAAa,UAAS,UAAS,EAAC,kBAAiB,CAAC,EAAC,SAAQ,4CAA2C,OAAM,aAAY,QAAO,EAAC,eAAc,EAAC,SAAQ,+BAA8B,QAAO,EAAC,GAAE,QAAO,UAAS,EAAC,GAAE,EAAC,SAAQ,kDAAiD,OAAM,cAAa,QAAO,EAAC,eAAc,EAAC,SAAQ,sDAAqD,YAAW,MAAG,QAAO,EAAC,GAAE,QAAO,UAAS,EAAC,CAAC,GAAE,MAAK,EAAC,SAAQ,8CAA6C,QAAO,KAAE,EAAC,CAAC,GAAE,OAAO,EAAE,UAAU,OAAO,QAAO,EAAE,UAAU,aAAa,UAAS,WAAU,EAAC,YAAW,EAAC,SAAQ,uCAAsC,OAAM,UAAS,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,UAAS,YAAW,EAAC,OAAM,EAAC,SAAQ,iBAAgB,OAAM,SAAQ,EAAC,CAAC,GAAE,EAAE,UAAU,KAAG,EAAE,UAAU,QAAO,EAAE,UAAU,MAAI,EAAE,UAAU;AAAM,EAAE,KAAK,GAAQ,gBAAU,IAAQ,gBAAU,OAAO,SAAQ,EAAC,SAAQ,EAAC,SAAQ,uEAAsE,QAAO,KAAE,GAAE,QAAO,EAAC,SAAQ,uCAAsC,QAAO,KAAE,GAAE,cAAa,EAAC,SAAQ,oFAAmF,YAAW,KAAE,GAAE,SAAQ,qVAAoV,UAAS,yBAAwB,QAAO,qHAAoH,UAAS,kDAAiD,CAAC,GAAQ,gBAAU,aAAa,KAAI,UAAS,EAAC,MAAK,EAAC,SAAQ,4CAA2C,QAAO,KAAE,EAAC,CAAC,GAAQ,gBAAU,aAAa,KAAI,UAAS,EAAC,OAAM,EAAC,SAAQ,6FAA4F,YAAW,MAAG,QAAO,MAAG,OAAM,YAAW,QAAO,EAAC,QAAO,CAAC,EAAC,SAAQ,4BAA2B,YAAW,KAAE,GAAQ,gBAAU,EAAE,MAAM,GAAE,MAAW,gBAAU,EAAE,MAAK,SAAc,gBAAU,EAAE,SAAQ,cAAa,CAAC,EAAC,SAAQ,gCAA+B,YAAW,KAAE,GAAE,EAAC,SAAQ,gCAA+B,YAAW,MAAG,OAAM,WAAU,CAAC,GAAE,WAAU,EAAC,SAAQ,iBAAgB,YAAW,MAAG,OAAM,UAAS,GAAE,kBAAiB,MAAK,aAAY,mBAAkB,YAAW,EAAC,SAAQ,aAAY,QAAa,gBAAU,EAAC,EAAC,EAAC,EAAC,CAAC,GAAQ,gBAAU,aAAa,KAAI,YAAW,EAAC,UAAS,6HAA4H,CAAC,GAAE,OAAa,gBAAU,EAAE,SAAc,gBAAU,aAAiB,gBAAU,OAAO,KAAI,EAAC,QAAO,EAAC,SAAQ,yCAAwC,QAAO,KAAE,GAAE,SAAQ,yYAAwY,UAAS,wDAAuD,CAAC,GAAE,OAAa,gBAAU,WAAW,YAAY,GAAQ,gBAAU,OAAW,gBAAU,YAAiB,gBAAU,SAAa,gBAAU,OAAO,SAAQ,EAAC,QAAO,EAAC,SAAQ,uCAAsC,QAAO,KAAE,GAAE,cAAa,cAAa,SAAQ,6RAA4R,UAAS,sGAAqG,CAAC,GAAQ,gBAAU,aAAa,UAAS,cAAa,EAAC,MAAK,EAAC,SAAQ,iEAAgE,QAAO,KAAE,GAAE,aAAY,yBAAwB,OAAM,EAAC,SAAQ,oBAAmB,OAAM,SAAQ,EAAC,CAAC,GAAE,OAAa,gBAAU,OAAO,UAAS,SAAS,GAAE;AAAC,WAAQ,IAAE,8CAA8C,QAAO,IAAE,GAAE,IAAE,GAAE;AAAI,QAAE,EAAE,QAAQ,WAAU,WAAU;AAAC,aAAO;AAAA,IAAC,CAAC;AAAE,MAAE,EAAE,QAAQ,WAAU,WAAU;AAAC,WAAM,UAAU;AAAA,EAAM,CAAC,GAAE,EAAE,UAAU,OAAK,EAAC,SAAQ,CAAC,EAAC,SAAQ,OAAO,YAAY,SAAO,CAAC,GAAE,YAAW,MAAG,QAAO,KAAE,GAAE,EAAC,SAAQ,oBAAmB,YAAW,MAAG,QAAO,KAAE,CAAC,GAAE,QAAO,EAAC,SAAQ,yDAAwD,QAAO,KAAE,GAAE,MAAK,EAAC,SAAQ,4EAA2E,QAAO,KAAE,GAAE,WAAU,EAAC,SAAQ,+CAA8C,QAAO,MAAG,OAAM,aAAY,QAAO,EAAC,QAAO,KAAI,EAAC,GAAE,kBAAiB,EAAC,SAAQ,4DAA2D,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,uBAAsB,EAAC,SAAQ,WAAU,OAAM,cAAa,GAAE,MAAK,KAAI,EAAC,GAAE,uBAAsB,EAAC,SAAQ,QAAO,OAAM,SAAQ,GAAE,sBAAqB,EAAC,SAAQ,kBAAiB,YAAW,MAAG,OAAM,cAAa,GAAE,UAAS,SAAQ,uBAAsB,EAAC,SAAQ,gBAAe,YAAW,MAAG,OAAM,WAAU,GAAE,mBAAkB,EAAC,SAAQ,8CAA6C,YAAW,MAAG,OAAM,aAAY,GAAE,sBAAqB,CAAC,EAAC,SAAQ,sCAAqC,YAAW,MAAG,OAAM,YAAW,GAAE,EAAC,SAAQ,yFAAwF,YAAW,MAAG,OAAM,aAAY,QAAO,EAAC,aAAY,KAAI,EAAC,CAAC,GAAE,SAAQ,CAAC,+RAA8R,8DAA8D,GAAE,UAAS,mCAAkC,OAAM,EAAC,SAAQ,UAAS,OAAM,WAAU,GAAE,UAAS,uBAAsB,cAAa,gBAAe,WAAU,EAAC,SAAQ,8DAA6D,QAAO,EAAC,aAAY,KAAI,EAAC,GAAE,QAAO,8KAA6K,SAAQ,sBAAqB,aAAY,oCAAmC,UAAS,sDAAqD,GAAE,EAAE,UAAU,KAAK,gBAAgB,EAAE,OAAO,OAAK,EAAE,UAAU,MAAK,EAAE,UAAU,KAAK,UAAU,OAAO,SAAO,EAAE,UAAU,KAAK;AAAM,EAAE,KAAK,GAAQ,gBAAU,KAAS,gBAAU,OAAO,SAAQ,EAAC,QAAO,EAAC,SAAQ,0CAAyC,YAAW,MAAG,QAAO,KAAE,GAAE,SAAQ,uKAAsK,SAAQ,iCAAgC,QAAO,CAAC,gCAA+B,iFAAgF,oEAAoE,GAAE,UAAS,yFAAwF,SAAQ,2LAA0L,CAAC,GAAQ,gBAAU,aAAa,MAAK,UAAS,EAAC,MAAK,EAAC,SAAQ,8BAA6B,QAAO,KAAE,EAAC,CAAC,GAAE,OAAa,gBAAU,GAAG,YAAY,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,qsBAAosB,IAAE,uCAAuC,OAAO,QAAQ,cAAa,WAAU;AAAC,WAAO,EAAE;AAAA,EAAM,CAAC;AAAE,IAAE,UAAU,MAAI,EAAE,UAAU,OAAO,KAAI,EAAC,cAAa,CAAC,EAAC,SAAQ,OAAO,gEAAgE,OAAO,QAAQ,cAAa,WAAU;AAAC,WAAO,EAAE;AAAA,EAAM,CAAC,CAAC,GAAE,YAAW,KAAE,GAAE,kCAAiC,qCAAoC,gEAAgE,GAAE,SAAQ,GAAE,QAAO,EAAC,SAAQ,kJAAiJ,QAAO,KAAE,GAAE,UAAS,0HAAyH,SAAQ,qBAAoB,CAAC,GAAE,EAAE,UAAU,aAAa,OAAM,UAAS,EAAC,QAAO,EAAC,SAAQ,OAAO,2BAA2B,SAAO,QAAM,mDAAmD,SAAO,MAAI,kDAAkD,OAAO,QAAQ,eAAc,WAAU;AAAC,WAAO;AAAA,EAAC,CAAC,IAAE,GAAG,GAAE,YAAW,MAAG,QAAO,MAAG,QAAO,EAAC,QAAO,gBAAe,UAAS,KAAI,aAAY,KAAI,EAAC,GAAE,cAAa,EAAC,SAAQ,qCAAoC,OAAM,UAAS,QAAO,KAAE,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,OAAM,WAAU,EAAC,oBAAmB,EAAC,SAAQ,+DAA8D,QAAO,EAAC,UAAS,QAAO,SAAQ,EAAC,SAAQ,YAAW,OAAM,cAAa,QAAO,EAAE,UAAU,IAAG,EAAC,EAAC,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,OAAM,YAAW,EAAC,gBAAe,EAAC,SAAQ,MAAK,OAAM,cAAa,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,OAAM,cAAa,EAAC,eAAc,EAAC,SAAQ,8EAA6E,YAAW,MAAG,QAAO,MAAG,QAAO,EAAE,UAAU,OAAO,OAAM,CAAC,CAAC,EAAC,EAAC,CAAC,GAAE,EAAE,UAAU,aAAa,UAAS,gBAAe,EAAC,cAAa,0BAAyB,GAAE,EAAE,UAAU,IAAI,aAAa,CAAC;AAAC,EAAE,KAAK,GAAQ,gBAAU,SAAO,EAAC,SAAQ,EAAC,SAAQ,gBAAe,YAAW,MAAG,QAAO,KAAE,GAAE,wBAAuB,EAAC,SAAQ,uEAAsE,QAAO,MAAG,QAAO,EAAC,eAAc,EAAC,SAAQ,uFAAsF,YAAW,MAAG,QAAO,EAAC,eAAc,EAAC,SAAQ,uBAAsB,YAAW,KAAE,GAAE,qBAAoB,EAAC,SAAQ,mBAAkB,OAAM,cAAa,GAAE,MAAK,KAAI,EAAC,GAAE,QAAO,UAAS,EAAC,GAAE,wBAAuB,EAAC,SAAQ,wCAAuC,QAAO,MAAG,OAAM,SAAQ,GAAE,QAAO,EAAC,SAAQ,oDAAmD,QAAO,KAAE,GAAE,UAAS,EAAC,SAAQ,6CAA4C,YAAW,KAAE,GAAE,cAAa,EAAC,SAAQ,oBAAmB,YAAW,KAAE,GAAE,WAAU,EAAC,SAAQ,4BAA2B,YAAW,MAAG,OAAM,CAAC,cAAa,aAAa,GAAE,QAAO,EAAC,aAAY,KAAI,EAAC,GAAE,SAAQ,yNAAwN,SAAQ,uhBAAshB,SAAQ,2BAA0B,QAAO,oJAAmJ,UAAS,wDAAuD,aAAY,gBAAe,GAAQ,gBAAU,OAAO,sBAAsB,EAAE,OAAO,cAAc,OAAO,OAAW,gBAAU,QAAa,gBAAU,KAAS,gBAAU,QAAa,gBAAU,OAAK,EAAC,UAAS,EAAC,SAAQ,0CAAyC,YAAW,MAAG,QAAO,KAAE,GAAE,QAAO,EAAC,SAAQ,0CAAyC,YAAW,MAAG,QAAO,KAAE,GAAE,SAAQ,EAAC,SAAQ,iCAAgC,QAAO,KAAE,GAAE,QAAO,sCAAqC,aAAY,YAAW,UAAS,KAAI,SAAQ,sBAAqB,MAAK,EAAC,SAAQ,YAAW,OAAM,UAAS,EAAC,GAAQ,gBAAU,cAAkB,gBAAU;;;ACHz/yD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACGA,IAAM,QAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,UAAU,YAAY,SAAS;AAAA,MACvC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,UAAU;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,eAAe,QAAQ;AAAA,MAC/B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,QAAQ,OAAO,UAAU;AAAA,MAC3C,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,UAAU;AAAA,MAC7B,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,kBAAQ;;;AC7Df,IAAMQ,SAAoB;AAAA,EACxB,OAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,WAAW,SAAS,aAAa;AAAA,MAC9D,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,OAAO,YAAY,QAAQ;AAAA,MACnC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,UAAU;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,YAAY,WAAW;AAAA,MACzC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,MAAM;AAAA,MAC3B,OAAO;AAAA,QACL,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,sBAAQA;;;AC/Ff,IAAMC,SAAoB;AAAA,EACxB,OAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,WAAW,SAAS,aAAa;AAAA,MAC9D,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,OAAO,YAAY,QAAQ;AAAA,MACnC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,UAAU;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,YAAY,WAAW;AAAA,MACzC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,eAAe,UAAU;AAAA,MACjC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,MAAM;AAAA,MAC3B,OAAO;AAAA,QACL,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,uBAAQA;;;ACtGf,IAAMC,SAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,WAAW,OAAO;AAAA,MAC/C,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,YAAY;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,eAAe,UAAU;AAAA,MACjC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,WAAW,aAAa,UAAU;AAAA,MACpD,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,WAAW,KAAK;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,mBAAmB;AAAA,MAC3B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,OAAO,YAAY,SAAS;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,iBAAQA;;;ACzEf,IAAMC,SAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,WAAW;AAAA,MAC/B,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,KAAK;AAAA,MACvB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,QAAQ,YAAY,UAAU;AAAA,MACjD,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA;AAAA;AAAA,MAGE,OAAO,CAAC,aAAa;AAAA,MACrB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,SAAS;AAAA,MAC7B,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,OAAO,YAAY,SAAS;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,mBAAQA;;;ACzGf,IAAMC,SAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,WAAW;AAAA,MAC/B,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,WAAW,QAAQ,YAAY,KAAK;AAAA,MACtD,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA;AAAA;AAAA,MAGE,OAAO,CAAC,aAAa;AAAA,MACrB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,YAAY,SAAS;AAAA,MACzC,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,YAAY,WAAW,WAAW;AAAA,MACtD,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,wBAAQA;;;AC3Ff,IAAM,SAAS;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,aAAa;AAAA,EACb,KAAK;AAAA,EACL,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,UAAU;AACZ;AACA,IAAMC,SAAoB;AAAA,EACxB,OAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa;AAAA,MACrB,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,QAAQ,WAAW,UAAU;AAAA,MACjD,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,UAAU,OAAO,UAAU;AAAA,MAC/C,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,YAAY;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,MAAM;AAAA,MACd,OAAO;AAAA,QACL,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,sBAAQA;;;AC1If,IAAMC,SAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,KAAK;AAAA,MACvB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,QAAQ,YAAY,YAAY,YAAY;AAAA,MAC/D,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa;AAAA,MACrB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,SAAS;AAAA,MAC7B,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,OAAO,YAAY,SAAS;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,OAAO,UAAU;AAAA,MACzB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,MAAM;AAAA,MACd,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,kBAAQA;;;ACxHf,IAAMC,SAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,UAAU;AAAA,MAC5B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,QAAQ,YAAY,UAAU;AAAA,MACjD,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,eAAe,UAAU;AAAA,MACjC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,cAAc,WAAW;AAAA,MACjC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,OAAO,SAAS;AAAA,MACxB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,oBAAQA;;;AC3Ff,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa;AAAA,MACrB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,KAAK;AAAA,MACvB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,SAAS;AAAA,MAC3B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,QAAQ,YAAY,YAAY,YAAY;AAAA,MAC/D,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,yBAAQA;;;ACtFf,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,WAAW,iBAAiB,UAAU,WAAW,OAAO;AAAA,MAChE,OAAO;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa;AAAA,MACrB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,UAAU;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO;AAAA,QACP,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,eAAe;AAAA,MACvB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,eAAe,UAAU;AAAA,MAC5C,OAAO;AAAA,QACL,OAAO;AAAA,QACP,YACE;AAAA,MACJ;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,cAAc,oBAAoB,SAAS;AAAA,MACnD,OAAO;AAAA,QACL,OAAO;AAAA,QACP,YACE;AAAA,MACJ;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,QAAQ;AAAA,MAC5B,OAAO;AAAA,QACL,OAAO;AAAA,QACP,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,UAAU,WAAW,gBAAgB;AAAA,MAC1D,OAAO;AAAA,QACL,OAAO;AAAA,QACP,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,QAAQ,cAAc,SAAS,UAAU;AAAA,MAC3D,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,KAAK;AAAA,MACvB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,MAAM;AAAA,MAC3B,OAAO;AAAA,QACL,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,sBAAQA;;;ACrIf,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,UAAU,WAAW,UAAU;AAAA,MACjD,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,cAAc,YAAY,OAAO,WAAW;AAAA,MACpD,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,mBAAQA;;;AC3Bf,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,WAAW,WAAW,2BAA2B;AAAA,MACpE,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,UAAU;AAAA,MAC5B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,UAAU;AAAA,MAC/B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,cAAc,sBAAsB;AAAA,MACjE,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA;AAAA,MAEE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA;AAAA,MAEE,OAAO,CAAC,KAAK;AAAA,MACb,WAAW,CAAC,QAAQ;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,eAAe,UAAU;AAAA,MACjC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA;AAAA,MAEE,OAAO,CAAC,aAAa;AAAA,MACrB,WAAW,CAAC,QAAQ;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,MAAM;AAAA,MACd,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,iBAAQA;;;ACvGf,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,YAAY,UAAU;AAAA,MACxC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,MAAM;AAAA,MAC1B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,QAAQ;AAAA,MAC3B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,aAAa;AAAA,MAChC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,SAAS;AAAA,MAC7B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,kBAAQA;;;ACtEf,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,WAAW,WAAW,2BAA2B;AAAA,MACpE,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,UAAU;AAAA,MAC5B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,UAAU;AAAA,MAC/B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,cAAc,sBAAsB;AAAA,MACjE,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,WAAW,CAAC,QAAQ;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,eAAe,UAAU;AAAA,MACjC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa;AAAA,MACrB,WAAW,CAAC,QAAQ;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,MAAM;AAAA,MACd,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,uBAAQA;;;ACtGf,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,WAAW,WAAW,2BAA2B;AAAA,MACpE,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,UAAU;AAAA,MAC5B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,UAAU;AAAA,MAC/B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,cAAc,sBAAsB;AAAA,MACjE,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,WAAW,CAAC,QAAQ;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,eAAe,UAAU;AAAA,MACjC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa;AAAA,MACrB,WAAW,CAAC,QAAQ;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,MAAM;AAAA,MACd,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,wBAAQA;;;ACjGf,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,OAAO;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,eAAe,QAAQ;AAAA,MAC1C,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO,EAAE,OAAO,oBAAoB;AAAA,IACtC;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO,EAAE,OAAO,qBAAqB;AAAA,IACvC;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,OAAO,UAAU,WAAW,WAAW;AAAA,MAC3D,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IAEA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,YAAY,UAAU;AAAA,MAC1C,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,MAAM;AAAA,MAC3B,OAAO;AAAA,QACL,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,kBAAQA;;;ACrGf,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,OAAO;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,eAAe,QAAQ;AAAA,MAC1C,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IAEA;AAAA,MACE,OAAO,CAAC,YAAY,OAAO,UAAU,WAAW,WAAW;AAAA,MAC3D,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,YAAY,UAAU;AAAA,MAC1C,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,SAAS;AAAA,MACjB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU;AAAA,MAClB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,MAAM;AAAA,MAC3B,OAAO;AAAA,QACL,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW;AAAA,MACnB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,mBAAQA;;;AC3Gf,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,iBAAiB;AAAA,MACzB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,aAAa,QAAQ,OAAO,OAAO;AAAA,MAC3C,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,cAAc,QAAQ;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,SAAS,YAAY,UAAU;AAAA,MAC5D,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,UAAU,QAAQ;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,8BAAQA;;;AC1Ef,IAAMC,UAAoB;AAAA,EACxB,OAAO;AAAA,IACL,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,YAAY;AAAA,MAC9B,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,iBAAiB;AAAA,MACzB,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,YAAY,aAAa,QAAQ,KAAK;AAAA,MAC9C,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,KAAK;AAAA,MACb,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,WAAW,UAAU,SAAS,YAAY,UAAU;AAAA,MAC5D,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO,CAAC,UAAU,UAAU,QAAQ;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,+BAAQA;;;AtB3Ef,IAAAC,gBAA8B;;;AuBD9B,mBAA4B;AAE5B,kBAAiB;AAEV,IAAM,kBAAkB,CAAC,wBAC9B;AAAA,EACE,CAAC,OAAwD;AAAxD,iBAAE,aAAW,OAAO,KAPzB,IAOK,IAA6B,iBAA7B,IAA6B,CAA3B,aAAW,SAAO;AACnB,UAAM,SAA0B,iCAC3B,OAD2B;AAAA,MAE9B,eAAW,YAAAC,SAAK,cAAc,SAAS;AAAA,IACzC;AAEA,QAAI,OAAO,oBAAoB,YAAY,WAAW;AACpD,aAAO,QAAQ,gBAAgB;AAEjC,QAAI,OAAO,UAAU;AACnB,aAAO,QAAQ,kCAAM,OAAO,SAAS,CAAC,IAAO;AAE/C,WAAO;AAAA,EACT;AAAA,EACA,CAAC,eAAe;AAClB;;;ACrBF,IAAAC,gBAA2C;AAE3C,IAAAC,eAAiB;AAEV,IAAM,mBAAmB,CAAC,oBAAgC;AAC/D,QAAM,oBAAgB;AAAA,IACpB,CAAC,EAAE,OAAO,MAAM,MAAa;AAC3B,UAAI,mBAAmB;AAAM,eAAO;AAAA,eAC3B,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,SAAS;AACnD,eAAO,SAAS,OAAO,EAAE,SAAS,eAAe,IAAI;AAAA,MACvD,WAAW,MAAM,WAAW,KAAK,SAAS,MAAM;AAC9C,eAAO,gBAAgB,MAAM,CAAC,CAAC;AAAA,MACjC;AAEA,aAAO,OAAO;AAAA,QACZ,SAAS,OAAO,EAAE,SAAS,eAAe,IAAI,CAAC;AAAA,QAC/C,GAAG,MAAM,IAAI,UAAQ,gBAAgB,IAAI,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,CAAC,eAAe;AAAA,EAClB;AAEA,aAAO;AAAA,IACL,CAAC,OAA0D;AAA1D,mBAAE,SAAO,WAAW,MAxBzB,IAwBK,IAA8B,iBAA9B,IAA8B,CAA5B,SAAO,aAAW;AACnB,YAAM,SAA2B,iCAC5B,OAD4B;AAAA,QAE/B,eAAW,aAAAC,SAAK,SAAS,GAAG,MAAM,OAAO,SAAS;AAAA,QAClD,UAAU,MAAM;AAAA,QAChB,OAAO,cAAc,KAAK;AAAA,MAC5B;AAEA,UAAI,SAAS,MAAM;AACjB,eAAO,QAAQ,kCACT,OAAO,SAAS,CAAC,IAClB;AAAA,MAEP;AAEA,aAAO;AAAA,IACT;AAAA,IACA,CAAC,aAAa;AAAA,EAChB;AACF;;;ACxCA,IAAM,YAAY;AAGlB,IAAM,sBAAsB,CAAC,SAAkB;AAC7C,MAAI,KAAK,WAAW,GAAG;AACrB,SAAK,KAAK;AAAA,MACR,OAAO,CAAC,OAAO;AAAA,MACf,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC;AAAA,EACH,WAAW,KAAK,WAAW,KAAK,KAAK,CAAC,EAAE,YAAY,IAAI;AACtD,SAAK,CAAC,EAAE,UAAU;AAClB,SAAK,CAAC,EAAE,QAAQ;AAAA,EAClB;AACF;AAEA,IAAM,cAAc,CAAC,OAAiB,QAAqC;AACzE,QAAM,YAAY,MAAM;AAExB,MAAI,YAAY,KAAK,MAAM,YAAY,CAAC,MAAM,KAAK;AACjD,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,OAAO,GAAG;AACzB;AAQA,IAAM,kBAAkB,CAAC,WAA+C;AACtE,QAAM,eAA2B,CAAC,CAAC,CAAC;AACpC,QAAM,gBAAgB,CAAC,MAAM;AAC7B,QAAM,qBAAqB,CAAC,CAAC;AAC7B,QAAM,oBAAoB,CAAC,OAAO,MAAM;AACxC,MAAI,IAAI;AACR,MAAI,aAAa;AACjB,MAAI,cAAuB,CAAC;AAC5B,QAAM,MAAM,CAAC,WAAW;AAExB,SAAO,aAAa,IAAI;AACtB,YACG,IAAI,mBAAmB,UAAU,OAAO,kBAAkB,UAAU,GACrE;AACA,UAAI;AACJ,UAAI,QAAQ,aAAa,UAAU;AACnC,YAAM,WAAW,cAAc,UAAU;AACzC,YAAM,QAAQ,SAAS,CAAC;AAGxB,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,aAAa,IAAI,QAAQ,CAAC,OAAO;AACzC,kBAAU;AAAA,MACZ,OAAO;AACL,gBAAQ,YAAY,OAAO,MAAM,IAAI;AAErC,YAAI,MAAM,OAAO;AACf,kBAAQ,YAAY,OAAO,MAAM,KAAK;AAAA,QACxC;AAEA,kBAAU,MAAM;AAAA,MAClB;AAGA,UAAI,OAAO,YAAY,UAAU;AAC/B;AACA,qBAAa,KAAK,KAAK;AACvB,sBAAc,KAAK,OAAuB;AAC1C,2BAAmB,KAAK,CAAC;AACzB,0BAAkB,KAAK,QAAQ,MAAM;AACrC;AAAA,MACF;AAGA,YAAM,kBAAkB,QAAQ,MAAM,SAAS;AAC/C,YAAM,eAAe,gBAAgB;AACrC,kBAAY,KAAK;AAAA,QACf;AAAA,QACA,SAAS,gBAAgB,CAAC;AAAA,MAC5B,CAAC;AAGD,eAASC,KAAI,GAAGA,KAAI,cAAcA,MAAK;AACrC,4BAAoB,WAAW;AAC/B,YAAI,KAAM,cAAc,CAAC,CAAE;AAC3B,oBAAY,KAAK;AAAA,UACf;AAAA,UACA,SAAS,gBAAgBA,EAAC;AAAA,QAC5B,CAAC;AAAA,MACH;AAAA,IACF;AAGA;AACA,iBAAa,IAAI;AACjB,kBAAc,IAAI;AAClB,uBAAmB,IAAI;AACvB,sBAAkB,IAAI;AAAA,EACxB;AAEA,sBAAoB,WAAW;AAC/B,SAAO;AACT;AAEA,IAAO,0BAAQ;;;AC3Gf,IAAAC,gBAAwB;AASjB,IAAM,cAAc,CAAC,EAAE,OAAO,MAAM,SAAS,SAAS,MAAe;AAC1E,aAAO,uBAAQ,MAAM;AACnB,QAAI,WAAW;AAAM,aAAO,wBAAgB,CAAC,IAAI,CAAC;AAElD,UAAM,cAAyB;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,CAAC;AAAA,IACX;AAEA,UAAM,MAAM,IAAI,mBAAmB,WAAW;AAC9C,gBAAY,SAAS,MAAM,SAAS,MAAM,OAAO;AACjD,UAAM,MAAM,IAAI,kBAAkB,WAAW;AAC7C,WAAO,wBAAgB,YAAY,MAAM;AAAA,EAC3C,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,EACF,CAAC;AACH;;;AC1BA,IAAM,cAAc,CAACC,SAAmB,aAAkC;AACxE,QAAM,EAAE,MAAM,IAAIA;AAClB,QAAM,YAAYA,QAAM,OAAO,OAAkB,CAAC,KAAK,eAAe;AACpE,UAAM,EAAE,WAAAC,YAAW,MAAM,IAAI;AAE7B,QAAIA,cAAa,CAACA,WAAU,SAAS,QAAQ,GAAG;AAC9C,aAAO;AAAA,IACT;AAEA,eAAW,MAAM,QAAQ,UAAQ;AAC/B,YAAM,WAAqB,kCAAK,IAAI,IAAI,IAAM;AAC9C,UAAI,IAAI,IAAI;AAAA,IACd,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,CAAc;AAElB,YAAU,OAAO;AACjB,YAAU,QAAQ,iCAAK,QAAL,EAAY,iBAAiB,OAAU;AACzD,SAAO;AACT;AAEA,IAAO,sBAAQ;;;ACtBR,IAAM,YAAY,CAAC;AAAA,EACxB;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA,OAAAC;AAAA,EACA;AACF,MAA8B;AAC5B,QAAM,WAAW,UAAU,YAAY;AACvC,QAAM,kBAAkB,oBAAYA,SAAO,QAAQ;AACnD,QAAM,eAAe,gBAAgB,eAAe;AACpD,QAAM,gBAAgB,iBAAiB,eAAe;AACtD,QAAM,UAAU,MAAM,UAAU,QAAQ;AACxC,QAAM,SAAS,YAAY,EAAE,OAAO,UAAU,MAAM,QAAQ,CAAC;AAE7D,SAAO,SAAS;AAAA,IACd;AAAA,IACA,WAAW,uBAAuB;AAAA,IAClC,OAAO,mBAAmB,OAAO,gBAAgB,OAAO,CAAC;AAAA,IACzD;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;A5BdA,IAAMC,aAAY,CAAC,cACjB,6BAAc,WAAmB,iCAC5B,QAD4B;AAAA,EAE/B,OAAO,MAAM,SAAU;AAAA,EACvB,OAAO,MAAM,SAAgB;AAAA,EAC7B,MAAM,MAAM;AAAA,EACZ,UAAU,MAAM;AAClB,EAAC;", "names": ["module", "Prism", "lang", "hooks", "Highlight", "e", "n", "t", "a", "r", "s", "l", "u", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "theme", "import_react", "clsx", "import_react", "import_clsx", "clsx", "i", "import_react", "theme", "languages", "theme", "Highlight"]}