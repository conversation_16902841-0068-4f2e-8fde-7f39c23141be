{"name": "tailwind-scrollbar", "version": "4.0.2", "description": "Tailwind plugin for styling scrollbars", "author": "<PERSON> <<EMAIL>>", "main": "src/index.js", "repository": "https://github.com/adoxography/tailwind-scrollbar", "homepage": "https://github.com/adoxography/tailwind-scrollbar", "license": "MIT", "private": false, "scripts": {"build-types": "rm src/*.d.ts && tsc", "lint": "eslint src", "prepare": "husky install", "test": "jest"}, "files": ["src/*"], "peerDependencies": {"tailwindcss": "4.x"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.0", "cross-env": "^7.0.3", "eslint": "^8.23.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.23.3", "eslint-plugin-jest": "^27.0.1", "eslint-plugin-jsdoc": "^44.2.3", "husky": "^8.0.1", "jest": "^29.0.1", "postcss": "^8.2.4", "typescript": "^5.0.4"}, "engines": {"node": ">=12.13.0"}, "dependencies": {"prism-react-renderer": "^2.4.1"}}