/**
 * Base resets to make the plugin's utilities work
 *
 * @param {typedefs.TailwindPlugin} tailwind - Tailwind's plugin object
 * @param {'standard' | 'peseudoelements'} preferredStrategy - The preferred
 *    scrollbar styling strategy: standards track or pseudoelements
 */
export function addBaseStyles({ addBase }: typedefs.TailwindPlugin, preferredStrategy: "standard" | "peseudoelements"): void;
/**
 * @param {typedefs.TailwindPlugin} tailwind - Tailwind's plugin object
 * @param {'standard' | 'peseudoelements'} preferredStrategy - The preferred
 *    scrollbar styling strategy: standards track or pseudoelements
 */
export function addBaseSizeUtilities({ addUtilities }: typedefs.TailwindPlugin, preferredStrategy: "standard" | "peseudoelements"): void;
/**
 * Adds scrollbar-COMPONENT-COLOR utilities for every scrollbar component.
 *
 * @param {typedefs.TailwindPlugin} tailwind - <PERSON>lwind's plugin object
 */
export function addColorUtilities({ matchUtilities, theme }: typedefs.TailwindPlugin): void;
/**
 * Adds scrollbar-COMPONENT-rounded-VALUE utilities for every scrollbar
 * component.
 *
 * @param {typedefs.TailwindPlugin} tailwind - Tailwind's plugin object
 */
export function addRoundedUtilities({ theme, matchUtilities }: typedefs.TailwindPlugin): void;
/**
 * Adds scrollbar-w-* and scrollbar-h-* utilities
 *
 * @param {typedefs.TailwindPlugin} tailwind - Tailwind's plugin object
 */
export function addSizeUtilities({ matchUtilities, theme }: typedefs.TailwindPlugin): void;
import typedefs = require("./typedefs");
