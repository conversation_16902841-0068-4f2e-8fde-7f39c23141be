{"hash": "2dcbb273", "configHash": "7463f8b5", "lockfileHash": "821d79f0", "browserHash": "447104f2", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "af26e042", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "7f8c1e71", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a6033ee3", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "180112d8", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "b3b57c3f", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "6e0ae8f1", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "6e9204e2", "needsInterop": false}, "js-cookie": {"src": "../../js-cookie/dist/js.cookie.mjs", "file": "js-cookie.js", "fileHash": "83ca2c80", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "4ff1da66", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "de4265ab", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "8bebcca4", "needsInterop": false}, "react-icons/bi": {"src": "../../react-icons/bi/index.mjs", "file": "react-icons_bi.js", "fileHash": "8c591620", "needsInterop": false}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "46879891", "needsInterop": false}, "react-icons/fc": {"src": "../../react-icons/fc/index.mjs", "file": "react-icons_fc.js", "fileHash": "e4c173c5", "needsInterop": false}, "react-icons/fi": {"src": "../../react-icons/fi/index.mjs", "file": "react-icons_fi.js", "fileHash": "add95761", "needsInterop": false}, "react-icons/si": {"src": "../../react-icons/si/index.mjs", "file": "react-icons_si.js", "fileHash": "3a9d3928", "needsInterop": false}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "1932d107", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "37941fcf", "needsInterop": false}, "react-select": {"src": "../../react-select/dist/react-select.esm.js", "file": "react-select.js", "fileHash": "35435bfe", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "8f54972d", "needsInterop": false}}, "chunks": {"chunk-5GI3BUZJ": {"file": "chunk-5GI3BUZJ.js"}, "chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-JNNNAK6O": {"file": "chunk-JNNNAK6O.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}